//=================================================================================================
/*!
//  \file PhysicalUnitsConverter.cpp
//  \brief Implementation of physical to lattice units conversion
*/
//=================================================================================================

#include "PhysicalUnitsConverter.h"
#include "../../Logging.h"
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <cmath>

namespace walberla {
namespace curvedboundary {

//=================================================================================================
//
//  CONSTRUCTOR
//
//=================================================================================================

PhysicalUnitsConverter::PhysicalUnitsConverter()
    : dx_phys_(1.0)
    , dt_phys_(1.0)
    , physicalDomainSize_(1.0, 1.0, 1.0)
    , latticeDomainSize_(100, 100, 100)
    , referenceLength_(1.0)
    , referenceVelocity_(1.0)
    , lengthScale_(1.0)
    , timeScale_(1.0)
    , velocityScale_(1.0)
{
    updateConversionFactors();
}

PhysicalUnitsConverter::PhysicalUnitsConverter(Real dx_phys, Real dt_phys, const Vector3<Real>& domainSize)
    : dx_phys_(dx_phys)
    , dt_phys_(dt_phys)
    , physicalDomainSize_(domainSize)
    , latticeDomainSize_(100, 100, 100)
    , referenceLength_(dx_phys)
    , referenceVelocity_(dx_phys / dt_phys)
    , lengthScale_(1.0)
    , timeScale_(1.0)
    , velocityScale_(1.0)
{
    updateConversionFactors();
}

//=================================================================================================
//
//  SETUP METHODS
//
//=================================================================================================

void PhysicalUnitsConverter::setPhysicalParameters(Real dx_phys, Real dt_phys)
{
    WALBERLA_CHECK_GREATER(dx_phys, 0.0, "Physical dx must be positive");
    WALBERLA_CHECK_GREATER(dt_phys, 0.0, "Physical dt must be positive");
    
    dx_phys_ = dx_phys;
    dt_phys_ = dt_phys;
    updateConversionFactors();
}

void PhysicalUnitsConverter::setDomainSizes(const Vector3<Real>& physicalSize, const Vector3<Uint>& latticeSize)
{
    WALBERLA_CHECK_GREATER(physicalSize[0], 0.0, "Physical domain size must be positive");
    WALBERLA_CHECK_GREATER(physicalSize[1], 0.0, "Physical domain size must be positive");
    WALBERLA_CHECK_GREATER(physicalSize[2], 0.0, "Physical domain size must be positive");
    
    physicalDomainSize_ = physicalSize;
    latticeDomainSize_ = latticeSize;
    updateConversionFactors();
}

void PhysicalUnitsConverter::setReferenceValues(Real refLength, Real refVelocity)
{
    WALBERLA_CHECK_GREATER(refLength, 0.0, "Reference length must be positive");
    WALBERLA_CHECK_GREATER(refVelocity, 0.0, "Reference velocity must be positive");
    
    referenceLength_ = refLength;
    referenceVelocity_ = refVelocity;
}

//=================================================================================================
//
//  UNIT CONVERSION
//
//=================================================================================================

Vector3<Real> PhysicalUnitsConverter::physicalToLattice(const Vector3<Real>& physPos) const
{
    return physPos / dx_phys_;
}

Vector3<Real> PhysicalUnitsConverter::latticeToPhysical(const Vector3<Real>& latticePos) const
{
    return latticePos * dx_phys_;
}

Vector3<Real> PhysicalUnitsConverter::physicalVelocityToLattice(const Vector3<Real>& physVel) const
{
    return physVel * dt_phys_ / dx_phys_;
}

Vector3<Real> PhysicalUnitsConverter::latticeVelocityToPhysical(const Vector3<Real>& latticeVel) const
{
    return latticeVel * dx_phys_ / dt_phys_;
}

Vector3<Real> PhysicalUnitsConverter::physicalForceToLattice(const Vector3<Real>& physForce) const
{
    // Force conversion: F_lattice = F_physical * dt²/(m * dx)
    // For LBM: assuming unit density, F_lattice = F_physical * dt²/dx
    Real conversionFactor = dt_phys_ * dt_phys_ / dx_phys_;
    return physForce * conversionFactor;
}

Vector3<Real> PhysicalUnitsConverter::latticeForceToPhysical(const Vector3<Real>& latticeForce) const
{
    Real conversionFactor = dx_phys_ / (dt_phys_ * dt_phys_);
    return latticeForce * conversionFactor;
}

//=================================================================================================
//
//  MESH SCALING METHODS
//
//=================================================================================================

std::vector<Real> PhysicalUnitsConverter::calculateMeshScaling(
    const Vector3<Real>& meshBBoxMin,
    const Vector3<Real>& meshBBoxMax,
    const std::vector<Real>& targetRegion,
    ScalingMode mode) const
{
    WALBERLA_CHECK_EQUAL(targetRegion.size(), 6, "Target region must have 6 elements [xmin,ymin,zmin,xmax,ymax,zmax]");
    
    Vector3<Real> regionMin(targetRegion[0], targetRegion[1], targetRegion[2]);
    Vector3<Real> regionMax(targetRegion[3], targetRegion[4], targetRegion[5]);
    
    return fitMeshToRegion(meshBBoxMin, meshBBoxMax, regionMin, regionMax, mode);
}

std::vector<Real> PhysicalUnitsConverter::fitMeshToRegion(
    const Vector3<Real>& meshBBoxMin,
    const Vector3<Real>& meshBBoxMax,
    const Vector3<Real>& regionMin,
    const Vector3<Real>& regionMax,
    ScalingMode mode) const
{
    // Convert physical mesh bounds to lattice
    Vector3<Real> meshMinLattice = physicalToLattice(meshBBoxMin);
    Vector3<Real> meshMaxLattice = physicalToLattice(meshBBoxMax);
    
    Vector3<Real> meshSize = meshMaxLattice - meshMinLattice;
    Vector3<Real> targetSize = regionMax - regionMin;
    
    Vector3<Real> scaleFactors;
    Vector3<Real> offset;
    
    switch(mode) {
        case ScalingMode::AUTO_FIT:
            // Scale to fit exactly in each dimension
            scaleFactors[0] = targetSize[0] / meshSize[0];
            scaleFactors[1] = targetSize[1] / meshSize[1];
            scaleFactors[2] = targetSize[2] / meshSize[2];
            break;
            
        case ScalingMode::PRESERVE_ASPECT:
            // Use minimum scale factor to preserve aspect ratio
            {
                Real minScale = std::min({targetSize[0] / meshSize[0],
                                        targetSize[1] / meshSize[1],
                                        targetSize[2] / meshSize[2]});
                scaleFactors = Vector3<Real>(minScale, minScale, minScale);
            }
            break;
            
        case ScalingMode::TARGET_SIZE:
            // Scale to match reference length
            {
                Real refScale = referenceLength_ / dx_phys_;
                Real currentRefDim = std::max({meshSize[0], meshSize[1], meshSize[2]});
                Real targetScale = refScale / currentRefDim;
                scaleFactors = Vector3<Real>(targetScale, targetScale, targetScale);
            }
            break;
            
        default:
            WALBERLA_ABORT("Unknown scaling mode");
    }
    
    // Calculate scaled mesh size
    Vector3<Real> scaledMeshSize = meshSize;
    scaledMeshSize[0] *= scaleFactors[0];
    scaledMeshSize[1] *= scaleFactors[1];
    scaledMeshSize[2] *= scaleFactors[2];
    
    // Calculate offset to center mesh in target region
    offset[0] = regionMin[0] + (targetSize[0] - scaledMeshSize[0]) * 0.5 - meshMinLattice[0] * scaleFactors[0];
    offset[1] = regionMin[1] + (targetSize[1] - scaledMeshSize[1]) * 0.5 - meshMinLattice[1] * scaleFactors[1];
    offset[2] = regionMin[2] + (targetSize[2] - scaledMeshSize[2]) * 0.5 - meshMinLattice[2] * scaleFactors[2];
    
    return {scaleFactors[0], scaleFactors[1], scaleFactors[2], offset[0], offset[1], offset[2]};
}

std::vector<Real> PhysicalUnitsConverter::scaleToCharacteristicLength(
    const Vector3<Real>& meshBBoxMin,
    const Vector3<Real>& meshBBoxMax,
    Real targetLength,
    Uint direction) const
{
    WALBERLA_CHECK_LESS(direction, 3, "Direction must be 0, 1, or 2");
    
    Vector3<Real> meshMinLattice = physicalToLattice(meshBBoxMin);
    Vector3<Real> meshMaxLattice = physicalToLattice(meshBBoxMax);
    Vector3<Real> meshSize = meshMaxLattice - meshMinLattice;
    
    Real scaleFactor = targetLength / meshSize[direction];
    
    // Center the mesh at domain center
    Vector3<Real> domainCenter = Vector3<Real>(latticeDomainSize_[0], latticeDomainSize_[1], latticeDomainSize_[2]) * 0.5;
    Vector3<Real> scaledMeshSize = meshSize * scaleFactor;
    Vector3<Real> meshCenter = (meshMinLattice + meshMaxLattice) * 0.5;
    
    Vector3<Real> offset = domainCenter - meshCenter * scaleFactor;
    
    return {scaleFactor, offset[0], offset[1], offset[2]};
}

//=================================================================================================
//
//  VALIDATION AND DIAGNOSTICS
//
//=================================================================================================

bool PhysicalUnitsConverter::validateScaling(const std::vector<Real>& scaling) const
{
    if(scaling.size() < 4) return false;
    
    Real scaleFactor = scaling[0];
    
    // Check for reasonable scale factors
    if(scaleFactor <= 0.0 || scaleFactor > 1000.0 || scaleFactor < 1e-6) {
        WALBERLA_LOG_WARNING("Unusual scale factor: " << scaleFactor);
        return false;
    }
    
    // Check if scaled mesh fits in domain
    if(scaling.size() >= 6) {
        // Uniform scaling validation
        Real maxScaledDim = std::max({scaling[0], scaling[1], scaling[2]}) * 
                           std::max({physicalDomainSize_[0], physicalDomainSize_[1], physicalDomainSize_[2]}) / dx_phys_;
        
        Real maxDomainDim = Real(std::max({latticeDomainSize_[0], latticeDomainSize_[1], latticeDomainSize_[2]}));
        
        if(maxScaledDim > maxDomainDim * 0.95) {
            WALBERLA_LOG_WARNING("Scaled mesh may be too large for domain");
            return false;
        }
    }
    
    return true;
}

Real PhysicalUnitsConverter::getReynoldsNumber(Real characteristicLength, Real characteristicVel, Real kinematicViscosity) const
{
    return characteristicLength * characteristicVel / kinematicViscosity;
}

Real PhysicalUnitsConverter::getMachNumber(Real velocity) const
{
    return velocity / cs_phys_;
}

void PhysicalUnitsConverter::printConversionSummary() const
{
    std::cout << "\n=== Physical Units Converter Summary ===" << std::endl;
    std::cout << std::fixed << std::setprecision(6);
    
    std::cout << "Physical Parameters:" << std::endl;
    std::cout << "  dx = " << dx_phys_ << " m" << std::endl;
    std::cout << "  dt = " << dt_phys_ << " s" << std::endl;
    std::cout << "  Domain size (physical) = " << physicalDomainSize_ << " m" << std::endl;
    std::cout << "  Domain size (lattice) = " << latticeDomainSize_ << " nodes" << std::endl;
    
    std::cout << "\nReference Values:" << std::endl;
    std::cout << "  Reference length = " << referenceLength_ << " m" << std::endl;
    std::cout << "  Reference velocity = " << referenceVelocity_ << " m/s" << std::endl;
    
    std::cout << "\nConversion Factors:" << std::endl;
    std::cout << "  Length scale = " << lengthScale_ << " (lattice/physical)" << std::endl;
    std::cout << "  Time scale = " << timeScale_ << " (lattice/physical)" << std::endl;
    std::cout << "  Velocity scale = " << velocityScale_ << " (lattice/physical)" << std::endl;
    
    std::cout << "\nCharacteristic Numbers:" << std::endl;
    std::cout << "  Lattice velocity = " << physicalVelocityToLattice(Vector3<Real>(referenceVelocity_, 0, 0))[0] << std::endl;
    std::cout << "  Mach number = " << getMachNumber(referenceVelocity_) << std::endl;
    std::cout << "========================================\n" << std::endl;
}

//=================================================================================================
//
//  PRIVATE METHODS
//
//=================================================================================================

void PhysicalUnitsConverter::updateConversionFactors()
{
    lengthScale_ = 1.0 / dx_phys_;
    timeScale_ = 1.0 / dt_phys_;
    velocityScale_ = dt_phys_ / dx_phys_;
    
    WALBERLA_CHECK(isValidConfiguration(), "Invalid converter configuration");
}

bool PhysicalUnitsConverter::isValidConfiguration() const
{
    return (dx_phys_ > 0.0 && dt_phys_ > 0.0 &&
            physicalDomainSize_[0] > 0.0 && physicalDomainSize_[1] > 0.0 && physicalDomainSize_[2] > 0.0 &&
            latticeDomainSize_[0] > 0 && latticeDomainSize_[1] > 0 && latticeDomainSize_[2] > 0);
}

} // namespace curvedboundary
} // namespace walberla