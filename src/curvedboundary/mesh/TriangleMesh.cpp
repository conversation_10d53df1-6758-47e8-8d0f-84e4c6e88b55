//=================================================================================================
/*!
//  \file TriangleMesh.cpp
//  \brief Implementation of triangle mesh data structure
*/
//=================================================================================================

#include "TriangleMesh.h"
#include "../../Logging.h"
#include <algorithm>
#include <limits>
#include <cmath>

namespace walberla {
namespace curvedboundary {

//=================================================================================================
//
//  TRIANGLE IMPLEMENTATION
//
//=================================================================================================

//*************************************************************************************************
bool Triangle::containsPoint2D(const Vector3<Real>& point, int axis) const
{
    // Project triangle and point onto 2D plane perpendicular to axis
    int u, v;
    if(axis == 0) { u = 1; v = 2; }      // Project onto YZ plane
    else if(axis == 1) { u = 0; v = 2; } // Project onto XZ plane
    else { u = 0; v = 1; }               // Project onto XY plane
    
    // Use barycentric coordinates
    Real v0u = vertices[2][u] - vertices[0][u];
    Real v0v = vertices[2][v] - vertices[0][v];
    Real v1u = vertices[1][u] - vertices[0][u];
    Real v1v = vertices[1][v] - vertices[0][v];
    Real v2u = point[u] - vertices[0][u];
    Real v2v = point[v] - vertices[0][v];
    
    Real invDenom = 1.0 / (v0u * v1v - v0v * v1u);
    Real a = (v2u * v1v - v2v * v1u) * invDenom;
    Real b = (v0u * v2v - v0v * v2u) * invDenom;
    
    return (a >= 0) && (b >= 0) && (a + b <= 1);
}
//*************************************************************************************************

//=================================================================================================
//
//  TRIANGLE MESH IMPLEMENTATION
//
//=================================================================================================

//*************************************************************************************************
TriangleMesh::TriangleMesh(const std::string& name)
    : name_(name)
    , totalArea_(0.0)
    , volume_(0.0)
    , isClosed_(false)
    , bboxMin_(std::numeric_limits<Real>::max())
    , bboxMax_(-std::numeric_limits<Real>::max())
    , position_(0.0)
    , velocity_(0.0)
    , angularVelocity_(0.0)
    , rotationCenter_(0.0)
    , referenceArea_(1.0)
    , referenceLength_(1.0)
    , forceOutputEnabled_(true)
    , octree_(nullptr)
    , hasPhysicalDimensions_(false)
    , physicalBBoxMin_(0.0)
    , physicalBBoxMax_(0.0)
    , isScaled_(false)
    , scalingInfo_("No scaling applied")
{
}
//*************************************************************************************************

//*************************************************************************************************
TriangleMesh::~TriangleMesh()
{
}
//*************************************************************************************************

//=================================================================================================
//
//  MESH CONSTRUCTION
//
//=================================================================================================

//*************************************************************************************************
void TriangleMesh::addTriangle(const Vector3<Real>& v0, 
                              const Vector3<Real>& v1, 
                              const Vector3<Real>& v2)
{
    Triangle tri;
    tri.vertices[0] = v0;
    tri.vertices[1] = v1;
    tri.vertices[2] = v2;
    tri.id = triangles_.size();
    tri.computeProperties();
    
    triangles_.push_back(tri);
}
//*************************************************************************************************

//*************************************************************************************************
Uint TriangleMesh::addVertex(const Vector3<Real>& vertex)
{
    vertices_.push_back(vertex);
    originalVertices_.push_back(vertex);
    return vertices_.size() - 1;
}
//*************************************************************************************************

//*************************************************************************************************
void TriangleMesh::addTriangle(Uint i0, Uint i1, Uint i2)
{
    if(i0 >= vertices_.size() || i1 >= vertices_.size() || i2 >= vertices_.size()) {
        LOG_ERROR("Invalid vertex indices in addTriangle");
        return;
    }
    
    addTriangle(vertices_[i0], vertices_[i1], vertices_[i2]);
}
//*************************************************************************************************

//*************************************************************************************************
void TriangleMesh::clear()
{
    triangles_.clear();
    vertices_.clear();
    originalVertices_.clear();
    totalArea_ = 0.0;
    volume_ = 0.0;
    isClosed_ = false;
    octree_.reset();
}
//*************************************************************************************************

//*************************************************************************************************
void TriangleMesh::finalize()
{
    LOG_INFO("Finalizing mesh: " << name_);
    
    // Compute bounding box
    computeBoundingBox();
    
    // Compute total area
    totalArea_ = 0.0;
    for(const auto& tri : triangles_) {
        totalArea_ += tri.area;
    }
    
    // Compute volume and check if mesh is closed
    computeVolume();
    
    // Set rotation center to mesh centroid
    rotationCenter_ = Vector3<Real>(0.0);
    for(const auto& tri : triangles_) {
        rotationCenter_ += tri.centroid * tri.area;
    }
    if(totalArea_ > 0) {
        rotationCenter_ /= totalArea_;
    }
    
    LOG_INFO("Mesh finalized: " << triangles_.size() << " triangles, " 
             << vertices_.size() << " vertices, area = " << totalArea_);
}
//*************************************************************************************************

//=================================================================================================
//
//  MESH PROPERTIES
//
//=================================================================================================

//*************************************************************************************************
void TriangleMesh::computeBoundingBox()
{
    bboxMin_ = Vector3<Real>(std::numeric_limits<Real>::max());
    bboxMax_ = Vector3<Real>(-std::numeric_limits<Real>::max());
    
    for(const auto& vertex : vertices_) {
        for(int i = 0; i < 3; ++i) {
            bboxMin_[i] = std::min(bboxMin_[i], vertex[i]);
            bboxMax_[i] = std::max(bboxMax_[i], vertex[i]);
        }
    }
}
//*************************************************************************************************

//*************************************************************************************************
void TriangleMesh::computeVolume()
{
    // Use divergence theorem: V = 1/3 * ∫ x·n dA
    volume_ = 0.0;
    
    for(const auto& tri : triangles_) {
        // Contribution of this triangle
        Real contrib = 0.0;
        for(int i = 0; i < 3; ++i) {
            contrib += tri.vertices[i].dot(tri.normal);
        }
        volume_ += contrib * tri.area / 3.0;
    }
    
    volume_ /= 3.0;  // Final division by 3
    
    // Check if mesh is closed (volume should be positive)
    isClosed_ = (volume_ > 0.0);
    
    if(!isClosed_) {
        LOG_WARNING("Mesh '" << name_ << "' appears to be open (volume = " << volume_ << ")");
    }
}
//*************************************************************************************************

//=================================================================================================
//
//  TRANSFORMATION
//
//=================================================================================================

//*************************************************************************************************
bool TriangleMesh::isMoving() const
{
    const Real eps = 1e-10;
    return (velocity_.length() > eps || angularVelocity_.length() > eps);
}
//*************************************************************************************************

//*************************************************************************************************
void TriangleMesh::updatePosition(Real dt)
{
    // Update position
    position_ += velocity_ * dt;
    
    // Update rotation if needed
    if(angularVelocity_.length() > 1e-10) {
        // For simplicity, use small angle approximation
        // For more accuracy, use quaternions or rotation matrices
        Real angle = angularVelocity_.length() * dt;
        Vector3<Real> axis = angularVelocity_.normalized();
        
        // Update vertices (simplified - assumes small rotations)
        transformVertices();
    }
    
    // Update triangle properties
    for(auto& tri : triangles_) {
        tri.computeProperties();
    }
}
//*************************************************************************************************

//*************************************************************************************************
Vector3<Real> TriangleMesh::getVelocityAt(const Vector3<Real>& point) const
{
    // V = V_translation + omega × (r - r_center)
    Vector3<Real> r = point - (position_ + rotationCenter_);
    return velocity_ + angularVelocity_.cross(r);
}
//*************************************************************************************************

//*************************************************************************************************
void TriangleMesh::transformVertices()
{
    // Apply translation and rotation to original vertices
    for(size_t i = 0; i < vertices_.size(); ++i) {
        vertices_[i] = originalVertices_[i] + position_;
        // Add rotation here if needed
    }
    
    // Update triangles with new vertex positions
    // (This would be implemented based on how triangles store vertex references)
}
//*************************************************************************************************

//=================================================================================================
//
//  SPATIAL QUERIES
//
//=================================================================================================

//*************************************************************************************************
void TriangleMesh::buildOctree()
{
    LOG_INFO("Building octree for mesh: " << name_);
    // Octree implementation would go here
    // For now, we'll use simple linear search
}
//*************************************************************************************************

//*************************************************************************************************
Uint TriangleMesh::findClosestTriangle(const Vector3<Real>& point, Real& distance) const
{
    Uint closestIdx = 0;
    Real minDist = std::numeric_limits<Real>::max();
    
    for(size_t i = 0; i < triangles_.size(); ++i) {
        const Triangle& tri = triangles_[i];
        
        // Simple distance to centroid (can be improved)
        Real dist = (point - tri.centroid).length();
        
        if(dist < minDist) {
            minDist = dist;
            closestIdx = i;
        }
    }
    
    distance = minDist;
    return closestIdx;
}
//*************************************************************************************************

//*************************************************************************************************
bool TriangleMesh::isPointInside(const Vector3<Real>& point) const
{
    if(!isClosed_) {
        LOG_WARNING("Cannot check if point is inside open mesh");
        return false;
    }
    
    // Ray casting algorithm
    int crossings = 0;
    Vector3<Real> rayDir(1.0, 0.0, 0.0);  // Cast ray in +X direction
    
    for(const auto& tri : triangles_) {
        // Check if ray intersects triangle
        // Simplified implementation - would need proper ray-triangle intersection
        Real dist = tri.distanceToPlane(point);
        if(std::abs(dist) < 1e-10) {
            // Point is on triangle
            return true;
        }
    }
    
    return (crossings % 2) == 1;
}
//*************************************************************************************************

//*************************************************************************************************
void TriangleMesh::getTrianglesInBox(const Vector3<Real>& min, 
                                     const Vector3<Real>& max,
                                     std::vector<Uint>& triangles) const
{
    triangles.clear();
    
    for(size_t i = 0; i < triangles_.size(); ++i) {
        const Triangle& tri = triangles_[i];
        
        // Check if triangle bounding box intersects query box
        Vector3<Real> triMin = tri.vertices[0];
        Vector3<Real> triMax = tri.vertices[0];
        
        for(int v = 1; v < 3; ++v) {
            for(int d = 0; d < 3; ++d) {
                triMin[d] = std::min(triMin[d], tri.vertices[v][d]);
                triMax[d] = std::max(triMax[d], tri.vertices[v][d]);
            }
        }
        
        // Check overlap
        bool overlap = true;
        for(int d = 0; d < 3; ++d) {
            if(triMax[d] < min[d] || triMin[d] > max[d]) {
                overlap = false;
                break;
            }
        }
        
        if(overlap) {
            triangles.push_back(i);
        }
    }
}
//*************************************************************************************************

//=================================================================================================
//
//  PHYSICAL-TO-LATTICE SCALING
//
//=================================================================================================

//*************************************************************************************************
void TriangleMesh::applyPhysicalToLatticeScaling(const std::vector<Real>& scalingParams)
{
    WALBERLA_CHECK_GREATER_EQUAL(scalingParams.size(), 4, "Scaling parameters must have at least 4 elements");
    
    if(scalingParams.size() == 4) {
        // Uniform scaling: [scale_factor, offset_x, offset_y, offset_z]
        scaleUniform(scalingParams[0], Vector3<Real>(scalingParams[1], scalingParams[2], scalingParams[3]));
    } else if(scalingParams.size() >= 6) {
        // Non-uniform scaling: [scale_x, scale_y, scale_z, offset_x, offset_y, offset_z]
        Vector3<Real> scaleFactors(scalingParams[0], scalingParams[1], scalingParams[2]);
        Vector3<Real> offset(scalingParams[3], scalingParams[4], scalingParams[5]);
        scaleNonUniform(scaleFactors, offset);
    }
    
    isScaled_ = true;
    scalingInfo_ = "Physical-to-lattice scaling applied";
}
//*************************************************************************************************

//*************************************************************************************************
void TriangleMesh::scaleUniform(Real scaleFactor, const Vector3<Real>& offset)
{
    WALBERLA_CHECK_GREATER(scaleFactor, 0.0, "Scale factor must be positive");
    
    // Store original vertices if not already stored
    if(originalVertices_.empty()) {
        originalVertices_ = vertices_;
    }
    
    // Scale and translate all vertices
    for(auto& vertex : vertices_) {
        vertex = vertex * scaleFactor + offset;
    }
    
    // Update triangle properties
    for(auto& triangle : triangles_) {
        for(int i = 0; i < 3; ++i) {
            triangle.vertices[i] = triangle.vertices[i] * scaleFactor + offset;
        }
        triangle.computeProperties();
    }
    
    // Update bounding box
    computeBoundingBox();
    
    // Update areas and volume
    totalArea_ *= scaleFactor * scaleFactor;
    volume_ *= scaleFactor * scaleFactor * scaleFactor;
    
    isScaled_ = true;
    scalingInfo_ = "Uniform scaling factor: " + std::to_string(scaleFactor);
}
//*************************************************************************************************

//*************************************************************************************************
void TriangleMesh::scaleNonUniform(const Vector3<Real>& scaleFactors, const Vector3<Real>& offset)
{
    WALBERLA_CHECK_GREATER(scaleFactors[0], 0.0, "Scale factors must be positive");
    WALBERLA_CHECK_GREATER(scaleFactors[1], 0.0, "Scale factors must be positive");
    WALBERLA_CHECK_GREATER(scaleFactors[2], 0.0, "Scale factors must be positive");
    
    // Store original vertices if not already stored
    if(originalVertices_.empty()) {
        originalVertices_ = vertices_;
    }
    
    // Scale and translate all vertices
    for(auto& vertex : vertices_) {
        vertex[0] = vertex[0] * scaleFactors[0] + offset[0];
        vertex[1] = vertex[1] * scaleFactors[1] + offset[1];
        vertex[2] = vertex[2] * scaleFactors[2] + offset[2];
    }
    
    // Update triangle properties
    for(auto& triangle : triangles_) {
        for(int i = 0; i < 3; ++i) {
            triangle.vertices[i][0] = triangle.vertices[i][0] * scaleFactors[0] + offset[0];
            triangle.vertices[i][1] = triangle.vertices[i][1] * scaleFactors[1] + offset[1];
            triangle.vertices[i][2] = triangle.vertices[i][2] * scaleFactors[2] + offset[2];
        }
        triangle.computeProperties();
    }
    
    // Update bounding box
    computeBoundingBox();
    
    // Update areas and volume (approximate for non-uniform scaling)
    Real areaScaleFactor = std::sqrt(scaleFactors[0] * scaleFactors[1] * scaleFactors[0] * scaleFactors[2] * scaleFactors[1] * scaleFactors[2]) / 3.0;
    Real volumeScaleFactor = scaleFactors[0] * scaleFactors[1] * scaleFactors[2];
    totalArea_ *= areaScaleFactor;
    volume_ *= volumeScaleFactor;
    
    isScaled_ = true;
    scalingInfo_ = "Non-uniform scaling: [" + std::to_string(scaleFactors[0]) + ", " + 
                   std::to_string(scaleFactors[1]) + ", " + std::to_string(scaleFactors[2]) + "]";
}
//*************************************************************************************************

//*************************************************************************************************
void TriangleMesh::centerAt(const Vector3<Real>& center)
{
    // Calculate current centroid
    Vector3<Real> currentCenter = (bboxMin_ + bboxMax_) * 0.5;
    Vector3<Real> offset = center - currentCenter;
    
    // Translate mesh
    scaleUniform(1.0, offset);
    
    scalingInfo_ = "Centered at: [" + std::to_string(center[0]) + ", " + 
                   std::to_string(center[1]) + ", " + std::to_string(center[2]) + "]";
}
//*************************************************************************************************

//*************************************************************************************************
void TriangleMesh::fitToBoundingBox(const Vector3<Real>& targetMin, const Vector3<Real>& targetMax, bool preserveAspect)
{
    Vector3<Real> currentSize = bboxMax_ - bboxMin_;
    Vector3<Real> targetSize = targetMax - targetMin;
    
    Vector3<Real> scaleFactors;
    
    if(preserveAspect) {
        // Use minimum scale factor to preserve aspect ratio
        Real minScale = std::min({targetSize[0] / currentSize[0],
                                 targetSize[1] / currentSize[1],
                                 targetSize[2] / currentSize[2]});
        scaleFactors = Vector3<Real>(minScale, minScale, minScale);
    } else {
        // Scale to fit exactly
        scaleFactors[0] = targetSize[0] / currentSize[0];
        scaleFactors[1] = targetSize[1] / currentSize[1];
        scaleFactors[2] = targetSize[2] / currentSize[2];
    }
    
    // Calculate offset to center in target box
    Vector3<Real> scaledSize = currentSize;
    scaledSize[0] *= scaleFactors[0];
    scaledSize[1] *= scaleFactors[1];
    scaledSize[2] *= scaleFactors[2];
    
    Vector3<Real> targetCenter = (targetMin + targetMax) * 0.5;
    Vector3<Real> currentCenter = (bboxMin_ + bboxMax_) * 0.5;
    Vector3<Real> scaledCenter = currentCenter;
    scaledCenter[0] *= scaleFactors[0];
    scaledCenter[1] *= scaleFactors[1];
    scaledCenter[2] *= scaleFactors[2];
    
    Vector3<Real> offset = targetCenter - scaledCenter;
    
    // Apply scaling
    if(preserveAspect) {
        scaleUniform(scaleFactors[0], offset);
    } else {
        scaleNonUniform(scaleFactors, offset);
    }
    
    scalingInfo_ = "Fitted to bounding box, preserve aspect: " + std::string(preserveAspect ? "true" : "false");
}
//*************************************************************************************************

//*************************************************************************************************
void TriangleMesh::setPhysicalDimensions(const Vector3<Real>& physicalMin, const Vector3<Real>& physicalMax)
{
    physicalBBoxMin_ = physicalMin;
    physicalBBoxMax_ = physicalMax;
    hasPhysicalDimensions_ = true;
}
//*************************************************************************************************

//*************************************************************************************************
bool TriangleMesh::getPhysicalDimensions(Vector3<Real>& physicalMin, Vector3<Real>& physicalMax) const
{
    if(hasPhysicalDimensions_) {
        physicalMin = physicalBBoxMin_;
        physicalMax = physicalBBoxMax_;
        return true;
    }
    return false;
}
//*************************************************************************************************

//*************************************************************************************************
std::string TriangleMesh::getScalingInfo() const
{
    std::string info = scalingInfo_;
    if(hasPhysicalDimensions_) {
        info += "\nPhysical dimensions: [" + std::to_string(physicalBBoxMin_[0]) + ", " + 
                std::to_string(physicalBBoxMin_[1]) + ", " + std::to_string(physicalBBoxMin_[2]) + "] to [" +
                std::to_string(physicalBBoxMax_[0]) + ", " + std::to_string(physicalBBoxMax_[1]) + ", " + 
                std::to_string(physicalBBoxMax_[2]) + "]";
    }
    if(isScaled_) {
        info += "\nCurrent lattice dimensions: [" + std::to_string(bboxMin_[0]) + ", " + 
                std::to_string(bboxMin_[1]) + ", " + std::to_string(bboxMin_[2]) + "] to [" +
                std::to_string(bboxMax_[0]) + ", " + std::to_string(bboxMax_[1]) + ", " + 
                std::to_string(bboxMax_[2]) + "]";
    }
    return info;
}
//*************************************************************************************************

} // namespace curvedboundary
} // namespace walberla