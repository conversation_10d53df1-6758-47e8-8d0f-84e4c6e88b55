//=================================================================================================
/*!
//  \file PhysicalUnitsConverter.h
//  \brief Physical to lattice units conversion for mesh scaling
*/
//=================================================================================================

#ifndef _WALBERLA_PHYSICAL_UNITS_CONVERTER_H
#define _WALBERLA_PHYSICAL_UNITS_CONVERTER_H

#include "../../Vector3.h"
#include "../../Definitions.h"
#include <string>
#include <map>

namespace walberla {
namespace curvedboundary {

//=================================================================================================
//
//  PHYSICAL UNITS CONVERTER CLASS
//
//=================================================================================================

/*!\brief Handles conversion between physical and lattice units
//
// This class provides functionality to convert mesh coordinates, velocities,
// and other physical quantities to lattice units based on simulation parameters.
*/
class PhysicalUnitsConverter
{
public:
    //**Type definitions***************************************************************************
    enum class ScalingMode {
        MANUAL,          //!< Manual specification of lattice coordinates
        AUTO_FIT,        //!< Automatically fit mesh to domain
        PRESERVE_ASPECT, //!< Scale while preserving aspect ratio
        TARGET_SIZE      //!< Scale to specific target size
    };
    
    //**Constructor********************************************************************************
    PhysicalUnitsConverter();
    
    /*!\brief Constructor with simulation parameters
    //
    // \param dx_phys    Physical lattice spacing (m)
    // \param dt_phys    Physical time step (s)
    // \param domainSize Physical domain size (m)
    */
    PhysicalUnitsConverter(Real dx_phys, Real dt_phys, const Vector3<Real>& domainSize);
    
    //**Setup methods******************************************************************************
    
    /*!\brief Set physical simulation parameters
    //
    // \param dx_phys    Physical lattice spacing (m)
    // \param dt_phys    Physical time step (s)
    */
    void setPhysicalParameters(Real dx_phys, Real dt_phys);
    
    /*!\brief Set domain sizes
    //
    // \param physicalSize  Physical domain size (m)
    // \param latticeSize   Lattice domain size (grid points)
    */
    void setDomainSizes(const Vector3<Real>& physicalSize, const Vector3<Uint>& latticeSize);
    
    /*!\brief Set reference values for scaling
    //
    // \param refLength     Reference length (m)
    // \param refVelocity   Reference velocity (m/s)
    */
    void setReferenceValues(Real refLength, Real refVelocity);
    
    //**Unit conversion****************************************************************************
    
    /*!\brief Convert physical position to lattice coordinates
    //
    // \param physPos  Physical position (m)
    // \return Lattice position
    */
    Vector3<Real> physicalToLattice(const Vector3<Real>& physPos) const;
    
    /*!\brief Convert lattice coordinates to physical position
    //
    // \param latticePos  Lattice position
    // \return Physical position (m)
    */
    Vector3<Real> latticeToPhysical(const Vector3<Real>& latticePos) const;
    
    /*!\brief Convert physical velocity to lattice velocity
    //
    // \param physVel  Physical velocity (m/s)
    // \return Lattice velocity
    */
    Vector3<Real> physicalVelocityToLattice(const Vector3<Real>& physVel) const;
    
    /*!\brief Convert lattice velocity to physical velocity
    //
    // \param latticeVel  Lattice velocity
    // \return Physical velocity (m/s)
    */
    Vector3<Real> latticeVelocityToPhysical(const Vector3<Real>& latticeVel) const;
    
    /*!\brief Convert physical force to lattice force
    //
    // \param physForce  Physical force (N)
    // \return Lattice force
    */
    Vector3<Real> physicalForceToLattice(const Vector3<Real>& physForce) const;
    
    /*!\brief Convert lattice force to physical force
    //
    // \param latticeForce  Lattice force
    // \return Physical force (N)
    */
    Vector3<Real> latticeForceToPhysical(const Vector3<Real>& latticeForce) const;
    
    //**Mesh scaling methods***********************************************************************
    
    /*!\brief Calculate scaling factor for a mesh
    //
    // \param meshBBoxMin   Mesh bounding box minimum (physical units)
    // \param meshBBoxMax   Mesh bounding box maximum (physical units)
    // \param targetRegion  Target lattice region [xmin,ymin,zmin,xmax,ymax,zmax]
    // \param mode          Scaling mode
    // \return Scaling parameters: [scale_x, scale_y, scale_z, offset_x, offset_y, offset_z]
    */
    std::vector<Real> calculateMeshScaling(
        const Vector3<Real>& meshBBoxMin,
        const Vector3<Real>& meshBBoxMax,
        const std::vector<Real>& targetRegion,
        ScalingMode mode = ScalingMode::PRESERVE_ASPECT) const;
    
    /*!\brief Fit mesh to lattice domain region
    //
    // \param meshBBoxMin   Mesh bounding box minimum (physical units)
    // \param meshBBoxMax   Mesh bounding box maximum (physical units)
    // \param regionMin     Target region minimum (lattice coordinates)
    // \param regionMax     Target region maximum (lattice coordinates)
    // \param mode          Scaling mode
    // \return [scale_factor, offset_x, offset_y, offset_z]
    */
    std::vector<Real> fitMeshToRegion(
        const Vector3<Real>& meshBBoxMin,
        const Vector3<Real>& meshBBoxMax,
        const Vector3<Real>& regionMin,
        const Vector3<Real>& regionMax,
        ScalingMode mode = ScalingMode::PRESERVE_ASPECT) const;
    
    /*!\brief Scale mesh to target characteristic length
    //
    // \param meshBBoxMin     Mesh bounding box minimum (physical units)
    // \param meshBBoxMax     Mesh bounding box maximum (physical units)
    // \param targetLength    Target characteristic length (lattice units)
    // \param direction       Primary scaling direction (0=x, 1=y, 2=z)
    // \return [scale_factor, offset_x, offset_y, offset_z]
    */
    std::vector<Real> scaleToCharacteristicLength(
        const Vector3<Real>& meshBBoxMin,
        const Vector3<Real>& meshBBoxMax,
        Real targetLength,
        Uint direction = 0) const;
    
    //**Validation and diagnostics*****************************************************************
    
    /*!\brief Check if scaling parameters are reasonable
    //
    // \param scaling  Scaling parameters from calculateMeshScaling
    // \return True if scaling is reasonable
    */
    bool validateScaling(const std::vector<Real>& scaling) const;
    
    /*!\brief Get Reynolds number based on current parameters
    //
    // \param characteristicLength  Characteristic length (physical)
    // \param characteristicVel     Characteristic velocity (physical)
    // \param kinematicViscosity    Kinematic viscosity (physical)
    // \return Reynolds number
    */
    Real getReynoldsNumber(Real characteristicLength, Real characteristicVel, Real kinematicViscosity) const;
    
    /*!\brief Get Mach number for compressible flow
    //
    // \param velocity  Flow velocity (physical)
    // \return Mach number
    */
    Real getMachNumber(Real velocity) const;
    
    /*!\brief Print conversion summary
    */
    void printConversionSummary() const;
    
    //**Accessor methods***************************************************************************
    
    /*!\brief Get physical dx
    */
    Real getPhysicalDx() const { return dx_phys_; }
    
    /*!\brief Get physical dt
    */
    Real getPhysicalDt() const { return dt_phys_; }
    
    /*!\brief Get physical domain size
    */
    const Vector3<Real>& getPhysicalDomainSize() const { return physicalDomainSize_; }
    
    /*!\brief Get lattice domain size
    */
    const Vector3<Uint>& getLatticeDomainSize() const { return latticeDomainSize_; }
    
    /*!\brief Get reference length
    */
    Real getReferenceLength() const { return referenceLength_; }
    
    /*!\brief Get reference velocity
    */
    Real getReferenceVelocity() const { return referenceVelocity_; }

private:
    //**Member variables***************************************************************************
    
    // Physical parameters
    Real dx_phys_;                        //!< Physical lattice spacing (m)
    Real dt_phys_;                        //!< Physical time step (s)
    Vector3<Real> physicalDomainSize_;    //!< Physical domain size (m)
    Vector3<Uint> latticeDomainSize_;     //!< Lattice domain size (grid points)
    
    // Reference values
    Real referenceLength_;                //!< Reference length (m)
    Real referenceVelocity_;              //!< Reference velocity (m/s)
    
    // Conversion factors
    Real lengthScale_;                    //!< Length scale factor (lattice/physical)
    Real timeScale_;                      //!< Time scale factor (lattice/physical)
    Real velocityScale_;                  //!< Velocity scale factor (lattice/physical)
    
    // Constants
    static constexpr Real cs_phys_ = 343.0;     //!< Speed of sound in air (m/s)
    static constexpr Real cs_lattice_ = 1.0/std::sqrt(3.0);  //!< Lattice speed of sound
    
    //**Private methods****************************************************************************
    
    /*!\brief Update conversion factors
    */
    void updateConversionFactors();
    
    /*!\brief Check if parameters are valid
    */
    bool isValidConfiguration() const;
};

} // namespace curvedboundary
} // namespace walberla

#endif // _WALBERLA_PHYSICAL_UNITS_CONVERTER_H