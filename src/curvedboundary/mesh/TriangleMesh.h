//=================================================================================================
/*!
//  \file TriangleMesh.h
//  \brief Triangle mesh data structure for curved boundaries
*/
//=================================================================================================

#ifndef _WALBERLA_TRIANGLE_MESH_H
#define _WALBERLA_TRIANGLE_MESH_H

#include "../../Vector3.h"
#include "../../Definitions.h"
#include <vector>
#include <string>
#include <memory>

namespace walberla {
namespace curvedboundary {

// Forward declarations
class TriangleOctree;

//=================================================================================================
//
//  TRIANGLE STRUCTURE
//
//=================================================================================================

/*!\brief Single triangle in a mesh
*/
struct Triangle
{
    Vector3<Real> vertices[3];    //!< Triangle vertices
    Vector3<Real> normal;         //!< Surface normal (outward pointing)
    Vector3<Real> centroid;       //!< Triangle centroid
    Real area;                    //!< Triangle area
    Uint id;                      //!< Unique triangle ID
    
    //**Constructor********************************************************************************
    Triangle()
        : area(0.0)
        , id(0)
    {}
    
    //**Utility functions**************************************************************************
    
    /*!\brief Compute centroid, normal, and area from vertices
    */
    void computeProperties()
    {
        // Centroid
        centroid = (vertices[0] + vertices[1] + vertices[2]) / 3.0;
        
        // Normal (cross product of two edges)
        Vector3<Real> edge1 = vertices[1] - vertices[0];
        Vector3<Real> edge2 = vertices[2] - vertices[0];
        normal = edge1.cross(edge2);
        
        // Area (half the magnitude of cross product)
        area = 0.5 * normal.length();
        
        // Normalize the normal
        if(area > 1e-10) {
            normal.normalize();
        }
    }
    
    /*!\brief Check if a point is inside the triangle (2D projection)
    */
    bool containsPoint2D(const Vector3<Real>& point, int axis) const;
    
    /*!\brief Get distance from point to triangle plane
    */
    Real distanceToPlane(const Vector3<Real>& point) const
    {
        return normal.dot(point - vertices[0]);
    }
};

//=================================================================================================
//
//  TRIANGLE MESH CLASS
//
//=================================================================================================

/*!\brief Triangle mesh representation for curved boundaries
//
// This class stores a triangulated surface mesh and provides functionality
// for spatial queries, transformations, and boundary condition setup.
*/
class TriangleMesh
{
public:
    //**Type definitions***************************************************************************
    typedef std::vector<Triangle> TriangleContainer;
    typedef std::vector<Vector3<Real>> VertexContainer;
    
    //**Constructor & Destructor*******************************************************************
    explicit TriangleMesh(const std::string& name = "");
    ~TriangleMesh();
    
    //**Mesh construction**************************************************************************
    
    /*!\brief Add a triangle to the mesh
    //
    // \param v0, v1, v2  Triangle vertices
    */
    void addTriangle(const Vector3<Real>& v0, 
                    const Vector3<Real>& v1, 
                    const Vector3<Real>& v2);
    
    /*!\brief Add a vertex to the vertex list
    //
    // \param vertex  Vertex position
    // \return Index of the added vertex
    */
    Uint addVertex(const Vector3<Real>& vertex);
    
    /*!\brief Add a triangle using vertex indices
    //
    // \param i0, i1, i2  Vertex indices
    */
    void addTriangle(Uint i0, Uint i1, Uint i2);
    
    /*!\brief Clear all mesh data
    */
    void clear();
    
    /*!\brief Finalize mesh construction
    //
    // Computes normals, areas, bounding box, etc.
    */
    void finalize();
    
    //**Mesh properties****************************************************************************
    
    /*!\brief Get number of triangles
    */
    Uint getNumTriangles() const { return triangles_.size(); }
    
    /*!\brief Get number of vertices
    */
    Uint getNumVertices() const { return vertices_.size(); }
    
    /*!\brief Get mesh name
    */
    const std::string& getName() const { return name_; }
    
    /*!\brief Get total surface area
    */
    Real getTotalArea() const { return totalArea_; }
    
    /*!\brief Get volume (only valid for closed meshes)
    */
    Real getVolume() const { return volume_; }
    
    /*!\brief Get bounding box minimum
    */
    const Vector3<Real>& getBoundingBoxMin() const { return bboxMin_; }
    
    /*!\brief Get bounding box maximum
    */
    const Vector3<Real>& getBoundingBoxMax() const { return bboxMax_; }
    
    //**Triangle access****************************************************************************
    
    /*!\brief Get triangle by index
    */
    const Triangle& getTriangle(Uint index) const { return triangles_[index]; }
    Triangle& getTriangle(Uint index) { return triangles_[index]; }
    
    /*!\brief Get all triangles
    */
    const TriangleContainer& getTriangles() const { return triangles_; }
    
    /*!\brief Get vertex by index
    */
    const Vector3<Real>& getVertex(Uint index) const { return vertices_[index]; }
    
    //**Transformation*****************************************************************************
    
    /*!\brief Set mesh position (translation)
    */
    void setPosition(const Vector3<Real>& position) { position_ = position; }
    
    /*!\brief Get mesh position
    */
    const Vector3<Real>& getPosition() const { return position_; }
    
    /*!\brief Set mesh velocity
    */
    void setVelocity(const Vector3<Real>& velocity) { velocity_ = velocity; }
    
    /*!\brief Get mesh velocity
    */
    const Vector3<Real>& getVelocity() const { return velocity_; }
    
    /*!\brief Set angular velocity
    */
    void setAngularVelocity(const Vector3<Real>& omega) { angularVelocity_ = omega; }
    
    /*!\brief Get angular velocity
    */
    const Vector3<Real>& getAngularVelocity() const { return angularVelocity_; }
    
    /*!\brief Check if mesh is moving
    */
    bool isMoving() const;
    
    /*!\brief Update mesh position based on velocities
    //
    // \param dt  Time step
    */
    void updatePosition(Real dt);
    
    /*!\brief Get velocity at a point on the mesh surface
    //
    // \param point  Point on the surface
    // \return Velocity at the point (including rotation)
    */
    Vector3<Real> getVelocityAt(const Vector3<Real>& point) const;
    
    //**Physical-to-Lattice Scaling****************************************************************
    
    /*!\brief Scale mesh from physical to lattice units
    //
    // \param scalingParams  [scale_x, scale_y, scale_z, offset_x, offset_y, offset_z]
    */
    void applyPhysicalToLatticeScaling(const std::vector<Real>& scalingParams);
    
    /*!\brief Scale mesh uniformly
    //
    // \param scaleFactor  Uniform scaling factor
    // \param offset       Translation offset after scaling
    */
    void scaleUniform(Real scaleFactor, const Vector3<Real>& offset = Vector3<Real>(0,0,0));
    
    /*!\brief Scale mesh non-uniformly
    //
    // \param scaleFactors  Scale factors for each axis
    // \param offset        Translation offset after scaling
    */
    void scaleNonUniform(const Vector3<Real>& scaleFactors, const Vector3<Real>& offset = Vector3<Real>(0,0,0));
    
    /*!\brief Center mesh at specified position
    //
    // \param center  Target center position
    */
    void centerAt(const Vector3<Real>& center);
    
    /*!\brief Fit mesh to bounding box
    //
    // \param targetMin  Target bounding box minimum
    // \param targetMax  Target bounding box maximum
    // \param preserveAspect  Whether to preserve aspect ratio
    */
    void fitToBoundingBox(const Vector3<Real>& targetMin, const Vector3<Real>& targetMax, bool preserveAspect = true);
    
    /*!\brief Set physical dimensions for unit conversion
    //
    // \param physicalMin  Physical bounding box minimum (m)
    // \param physicalMax  Physical bounding box maximum (m)
    */
    void setPhysicalDimensions(const Vector3<Real>& physicalMin, const Vector3<Real>& physicalMax);
    
    /*!\brief Get physical dimensions
    //
    // \param physicalMin  Physical bounding box minimum (output)
    // \param physicalMax  Physical bounding box maximum (output)
    // \return True if physical dimensions are set
    */
    bool getPhysicalDimensions(Vector3<Real>& physicalMin, Vector3<Real>& physicalMax) const;
    
    /*!\brief Check if mesh has been scaled
    */
    bool isScaled() const { return isScaled_; }
    
    /*!\brief Get scaling information
    */
    std::string getScalingInfo() const;
    
    //**Force calculation**************************************************************************
    
    /*!\brief Set reference area for force coefficients
    */
    void setReferenceArea(Real area) { referenceArea_ = area; }
    
    /*!\brief Get reference area
    */
    Real getReferenceArea() const { return referenceArea_; }
    
    /*!\brief Set reference length for moment coefficients
    */
    void setReferenceLength(Real length) { referenceLength_ = length; }
    
    /*!\brief Get reference length
    */
    Real getReferenceLength() const { return referenceLength_; }
    
    /*!\brief Set reference values
    */
    void setReferenceValues(Real area, Real length) 
    { 
        referenceArea_ = area; 
        referenceLength_ = length; 
    }
    
    /*!\brief Enable/disable force output
    */
    void setForceOutputEnabled(bool enabled) { forceOutputEnabled_ = enabled; }
    
    /*!\brief Check if force output is enabled
    */
    bool isForceOutputEnabled() const { return forceOutputEnabled_; }
    
    //**Spatial queries****************************************************************************
    
    /*!\brief Build octree for spatial acceleration
    */
    void buildOctree();
    
    /*!\brief Find closest triangle to a point
    //
    // \param point     Query point
    // \param distance  Distance to closest triangle (output)
    // \return Index of closest triangle
    */
    Uint findClosestTriangle(const Vector3<Real>& point, Real& distance) const;
    
    /*!\brief Check if a point is inside the mesh (for closed meshes)
    //
    // \param point  Query point
    // \return True if point is inside
    */
    bool isPointInside(const Vector3<Real>& point) const;
    
    /*!\brief Get triangles within a bounding box
    //
    // \param min  Bounding box minimum
    // \param max  Bounding box maximum
    // \param triangles  Output triangle indices
    */
    void getTrianglesInBox(const Vector3<Real>& min, 
                          const Vector3<Real>& max,
                          std::vector<Uint>& triangles) const;
    
private:
    //**Private member functions*******************************************************************
    
    /*!\brief Compute bounding box
    */
    void computeBoundingBox();
    
    /*!\brief Compute mesh volume and check if closed
    */
    void computeVolume();
    
    /*!\brief Transform vertices to world coordinates
    */
    void transformVertices();
    
    //**Member variables***************************************************************************
    
    // Mesh data
    std::string name_;
    TriangleContainer triangles_;
    VertexContainer vertices_;
    
    // Original vertices (before transformation)
    VertexContainer originalVertices_;
    
    // Mesh properties
    Real totalArea_;
    Real volume_;
    bool isClosed_;
    Vector3<Real> bboxMin_;
    Vector3<Real> bboxMax_;
    
    // Transformation
    Vector3<Real> position_;
    Vector3<Real> velocity_;
    Vector3<Real> angularVelocity_;
    Vector3<Real> rotationCenter_;  // Center of rotation (usually centroid)
    
    // Reference values for force coefficients
    Real referenceArea_;
    Real referenceLength_;
    bool forceOutputEnabled_;
    
    // Spatial acceleration structure
    std::unique_ptr<TriangleOctree> octree_;
    
    // Physical-to-lattice scaling
    bool hasPhysicalDimensions_;
    Vector3<Real> physicalBBoxMin_;
    Vector3<Real> physicalBBoxMax_;
    bool isScaled_;
    std::string scalingInfo_;
};

} // namespace curvedboundary
} // namespace walberla

#endif // _WALBERLA_TRIANGLE_MESH_H