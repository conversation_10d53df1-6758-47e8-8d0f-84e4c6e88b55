//=================================================================================================
/*!
//  \file ScalingExample.cpp
//  \brief Example demonstrating physical-to-lattice scaling for curved boundaries
*/
//=================================================================================================

#include "../CurvedBoundary.h"
#include "../mesh/PhysicalUnitsConverter.h"
#include "../readers/STLReader.h"
#include "../../Definitions.h"
#include "../../SimData.h"
#include "../../Domain.h"
#include <iostream>
#include <memory>

using namespace walberla;
using namespace walberla::curvedboundary;

//=================================================================================================
//
//  EXAMPLE: AUTOMATIC MESH SCALING
//
//=================================================================================================

void demonstrateBasicScaling()
{
    std::cout << "\n=== Basic Mesh Scaling Example ===\n";
    
    // Setup simulation parameters (from your parameter file)
    Real dx_phys = 0.00312;      // Physical lattice spacing (m)
    Real dt_phys = 0.0005832;    // Physical time step (s)
    Real Lref = 0.04;            // Cylinder diameter (m)
    Real Uref = 0.535;           // Reference velocity (m/s)
    
    Vector3<Uint> domainSizeLattice(104, 364, 533);
    Vector3<Real> domainSizePhysical = Vector3<Real>(domainSizeLattice[0] * dx_phys,
                                                    domainSizeLattice[1] * dx_phys,
                                                    domainSizeLattice[2] * dx_phys);
    
    // Create units converter
    PhysicalUnitsConverter converter(dx_phys, dt_phys, domainSizePhysical);
    converter.setDomainSizes(domainSizePhysical, domainSizeLattice);
    converter.setReferenceValues(Lref, Uref);
    
    // Print conversion summary
    converter.printConversionSummary();
    
    // Create STL reader with enhanced options
    STLReader reader;
    
    // Configure preprocessing options
    STLReader::PreprocessingOptions preprocessOpts;
    preprocessOpts.computeNormals = true;
    preprocessOpts.checkNormalConsistency = true;
    preprocessOpts.removeDuplicateVertices = true;
    preprocessOpts.centerMesh = false;
    preprocessOpts.verbose = true;
    
    // Configure scaling options
    STLReader::ScalingOptions scalingOpts;
    scalingOpts.enableAutoScaling = true;
    scalingOpts.mode = PhysicalUnitsConverter::ScalingMode::PRESERVE_ASPECT;
    scalingOpts.characteristicLength = 26.0;  // Target lattice units
    scalingOpts.physicalPosition = Vector3<Real>(52.0 * dx_phys, 182.0 * dx_phys, 130.0 * dx_phys);
    scalingOpts.setPhysicalDimensions = true;
    scalingOpts.converter = std::make_shared<PhysicalUnitsConverter>(converter);
    
    reader.setPreprocessingOptions(preprocessOpts);
    reader.setScalingOptions(scalingOpts);
    
    // Load mesh (assuming Cylinder.stl exists)
    TriangleMesh mesh;
    bool success = reader.read("Cylinder.stl", mesh);
    
    if(success) {
        std::cout << reader.getMeshStatistics(mesh) << std::endl;
        std::cout << "Preprocessing Report:\n" << reader.getPreprocessingReport() << std::endl;
    } else {
        std::cout << "Note: Cylinder.stl not found - this is just a demonstration of the API\n";
    }
}

//=================================================================================================
//
//  EXAMPLE: TARGET REGION SCALING
//
//=================================================================================================

void demonstrateTargetRegionScaling()
{
    std::cout << "\n=== Target Region Scaling Example ===\n";
    
    // Setup converter
    PhysicalUnitsConverter converter(0.00312, 0.0005832, Vector3<Real>(0.32448, 1.13808, 1.66596));
    
    // Define target region in lattice coordinates
    // This corresponds to placing the object in a specific region of the domain
    std::vector<Real> targetRegion = {
        40.0, 150.0, 100.0,  // min: x, y, z
        80.0, 220.0, 160.0   // max: x, y, z
    };
    
    // Create mesh (for demonstration)
    TriangleMesh mesh("Example");
    // Add a simple box mesh for demonstration
    mesh.addTriangle(Vector3<Real>(0, 0, 0), Vector3<Real>(1, 0, 0), Vector3<Real>(0, 1, 0));
    mesh.addTriangle(Vector3<Real>(1, 1, 1), Vector3<Real>(0, 1, 1), Vector3<Real>(1, 0, 1));
    mesh.finalize();
    
    // Calculate scaling parameters
    Vector3<Real> meshMin = mesh.getBoundingBoxMin();
    Vector3<Real> meshMax = mesh.getBoundingBoxMax();
    
    auto scalingParams = converter.calculateMeshScaling(
        meshMin, meshMax, targetRegion, 
        PhysicalUnitsConverter::ScalingMode::PRESERVE_ASPECT);
    
    std::cout << "Original mesh bounds: [" << meshMin[0] << ", " << meshMin[1] << ", " << meshMin[2] 
              << "] to [" << meshMax[0] << ", " << meshMax[1] << ", " << meshMax[2] << "]\n";
    
    std::cout << "Scaling parameters: [";
    for(size_t i = 0; i < scalingParams.size(); ++i) {
        std::cout << scalingParams[i];
        if(i < scalingParams.size() - 1) std::cout << ", ";
    }
    std::cout << "]\n";
    
    // Apply scaling
    mesh.applyPhysicalToLatticeScaling(scalingParams);
    
    Vector3<Real> newMin = mesh.getBoundingBoxMin();
    Vector3<Real> newMax = mesh.getBoundingBoxMax();
    
    std::cout << "Scaled mesh bounds: [" << newMin[0] << ", " << newMin[1] << ", " << newMin[2] 
              << "] to [" << newMax[0] << ", " << newMax[1] << ", " << newMax[2] << "]\n";
    
    std::cout << mesh.getScalingInfo() << std::endl;
}

//=================================================================================================
//
//  EXAMPLE: PARAMETER FILE CONFIGURATION
//
//=================================================================================================

void demonstrateParameterFileConfiguration()
{
    std::cout << "\n=== Parameter File Configuration Example ===\n";
    
    // Simulate parameter file reading
    std::map<std::string, std::string> params;
    params["reComputeTheNormals"] = "1";
    params["checkNormConsistancy"] = "1";  // Note: preserving the typo from your file
    params["enableAutoScaling"] = "1";
    params["characteristicLength"] = "25.0";
    params["targetRegion"] = "50,180,120,70,200,140";
    
    STLReader reader;
    reader.configureFromParameters(params);
    
    std::cout << "Configured STL reader from parameter map:\n";
    std::cout << "- Normals computation: enabled\n";
    std::cout << "- Normal consistency check: enabled\n";
    std::cout << "- Auto scaling: enabled\n";
    std::cout << "- Characteristic length: 25.0 lattice units\n";
    std::cout << "- Target region: [50,180,120] to [70,200,140]\n";
}

//=================================================================================================
//
//  EXAMPLE: MESH ANALYSIS
//
//=================================================================================================

void demonstrateMeshAnalysis()
{
    std::cout << "\n=== Mesh Analysis Example ===\n";
    
    STLReader reader;
    
    // Analyze STL file without loading full mesh
    std::string analysis = reader.analyzeSTL("Cylinder.stl");
    std::cout << analysis << std::endl;
    
    // For a real mesh, you would get detailed statistics
    std::cout << "This analysis helps you determine:\n";
    std::cout << "- Memory requirements\n";
    std::cout << "- Mesh complexity\n";
    std::cout << "- File format\n";
    std::cout << "- Approximate processing time\n";
}

//=================================================================================================
//
//  EXAMPLE: INTEGRATION WITH YOUR CURRENT SYSTEM
//
//=================================================================================================

void demonstrateIntegrationWithCurrentSystem()
{
    std::cout << "\n=== Integration with Current System ===\n";
    
    // This shows how to integrate with your existing parameter file structure
    
    // Physical parameters from your SimData
    struct SimDataExample {
        Uint domainX = 104;
        Uint domainY = 364;
        Uint domainZ = 533;
        Real dt = 0.0005832;
        Real dx = 0.00312;
        Real Uref = 0.535;
        Real Lref = 0.04;
        Real inflow_L = 0.1;
    } simdata;
    
    // Create converter from your simulation data
    Vector3<Real> physicalDomain(simdata.domainX * simdata.dx,
                                simdata.domainY * simdata.dx,
                                simdata.domainZ * simdata.dx);
    Vector3<Uint> latticeDomain(simdata.domainX, simdata.domainY, simdata.domainZ);
    
    PhysicalUnitsConverter converter(simdata.dx, simdata.dt, physicalDomain);
    converter.setDomainSizes(physicalDomain, latticeDomain);
    converter.setReferenceValues(simdata.Lref, simdata.Uref);
    
    // Enhanced object configuration (backward compatible with your format)
    struct ObjectConfig {
        std::string stlFileName = "Cylinder.stl";
        Real density = 2000.0;
        Uint dynamic = 2;
        
        // Traditional parameters (still supported)
        Vector3<Real> position_L = Vector3<Real>(52.0, 182.0, 130.0);
        Real objectdomainZ = 26.0;
        
        // New enhanced parameters
        bool enableAutoScaling = true;
        PhysicalUnitsConverter::ScalingMode scalingMode = PhysicalUnitsConverter::ScalingMode::PRESERVE_ASPECT;
        bool reComputeTheNormals = true;
        bool checkNormConsistancy = true;
        
        Vector3<Real> initial_speed_L = Vector3<Real>(0.01, 0.02, 0.09);
    } objConfig;
    
    // Setup STL reader with your configuration
    STLReader reader;
    
    STLReader::PreprocessingOptions preprocessOpts;
    preprocessOpts.computeNormals = objConfig.reComputeTheNormals;
    preprocessOpts.checkNormalConsistency = objConfig.checkNormConsistancy;
    preprocessOpts.verbose = true;
    
    STLReader::ScalingOptions scalingOpts;
    scalingOpts.enableAutoScaling = objConfig.enableAutoScaling;
    scalingOpts.mode = objConfig.scalingMode;
    scalingOpts.characteristicLength = objConfig.objectdomainZ;
    scalingOpts.physicalPosition = Vector3<Real>(objConfig.position_L[0] * simdata.dx,
                                                 objConfig.position_L[1] * simdata.dx,
                                                 objConfig.position_L[2] * simdata.dx);
    scalingOpts.setPhysicalDimensions = true;
    scalingOpts.converter = std::make_shared<PhysicalUnitsConverter>(converter);
    
    reader.setPreprocessingOptions(preprocessOpts);
    reader.setScalingOptions(scalingOpts);
    
    // Load mesh with enhanced capabilities
    TriangleMesh mesh;
    if(reader.read(objConfig.stlFileName, mesh)) {
        std::cout << "Successfully loaded and processed mesh:\n";
        std::cout << reader.getMeshStatistics(mesh) << std::endl;
        std::cout << "Ready for voxelization and boundary condition setup\n";
    } else {
        std::cout << "Mesh file not found, but configuration is ready\n";
    }
    
    std::cout << "\nKey Benefits:\n";
    std::cout << "- Automatic scaling from physical to lattice units\n";
    std::cout << "- Mesh preprocessing and validation\n";
    std::cout << "- Backward compatibility with existing parameter files\n";
    std::cout << "- Enhanced error checking and reporting\n";
    std::cout << "- Support for multiple scaling modes\n";
}

//=================================================================================================
//
//  MAIN FUNCTION
//
//=================================================================================================

int main()
{
    std::cout << "Enhanced Mesh Scaling System Examples\n";
    std::cout << "=====================================\n";
    
    try {
        demonstrateBasicScaling();
        demonstrateTargetRegionScaling();
        demonstrateParameterFileConfiguration();
        demonstrateMeshAnalysis();
        demonstrateIntegrationWithCurrentSystem();
        
        std::cout << "\n=== Summary ===\n";
        std::cout << "The enhanced scaling system provides:\n";
        std::cout << "1. Automatic physical-to-lattice unit conversion\n";
        std::cout << "2. Advanced mesh preprocessing (normal computation, validation)\n";
        std::cout << "3. Multiple scaling modes (preserve aspect, fit to region, etc.)\n";
        std::cout << "4. Parameter file integration\n";
        std::cout << "5. Comprehensive error checking and reporting\n";
        std::cout << "6. Full backward compatibility with existing code\n";
        
    } catch(const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}