//=================================================================================================
/*!
//  \file STLReader.cpp
//  \brief Implementation of STL file reader
*/
//=================================================================================================

#include "STLReader.h"
#include "../../Logging.h"
#include "../../Convert.h"
#include <fstream>
#include <sstream>
#include <cstring>
#include <algorithm>
#include <set>
#include <cmath>
#include <iomanip>
#include <unordered_map>

namespace walberla {
namespace curvedboundary {

//=================================================================================================
//
//  STL READER IMPLEMENTATION
//
//=================================================================================================

//*************************************************************************************************
STLReader::STLReader()
    : preprocessingReport_("No preprocessing performed")
{
    // Set default preprocessing options
    preprocessingOpts_.computeNormals = true;
    preprocessingOpts_.checkNormalConsistency = true;
    preprocessingOpts_.removeDuplicateVertices = true;
    preprocessingOpts_.repairMesh = false;
    preprocessingOpts_.centerMesh = false;
    preprocessingOpts_.validateGeometry = true;
    preprocessingOpts_.degenerateThreshold = 1e-10;
    preprocessingOpts_.verbose = false;
    
    // Set default scaling options
    scalingOpts_.enableAutoScaling = false;
    scalingOpts_.mode = PhysicalUnitsConverter::ScalingMode::PRESERVE_ASPECT;
    scalingOpts_.characteristicLength = 1.0;
    scalingOpts_.physicalPosition = Vector3<Real>(0,0,0);
    scalingOpts_.setPhysicalDimensions = false;
    scalingOpts_.converter = nullptr;
}
//*************************************************************************************************

//*************************************************************************************************
bool STLReader::read(const std::string& filename, TriangleMesh& mesh)
{
    LOG_INFO("Reading STL file: " << filename);
    
    // Check if file exists
    std::ifstream file(filename, std::ios::binary);
    if(!file.is_open()) {
        LOG_ERROR("Failed to open STL file: " << filename);
        return false;
    }
    file.close();
    
    // Clear existing mesh data
    mesh.clear();
    
    // Determine file format and read
    bool success;
    if(isBinarySTL(filename)) {
        LOG_INFO("Detected binary STL format");
        success = readBinary(filename, mesh);
    } else {
        LOG_INFO("Detected ASCII STL format");
        success = readASCII(filename, mesh);
    }
    
    if(success) {
        // Apply preprocessing if enabled
        applyPreprocessing(mesh, preprocessingOpts_);
        
        // Apply scaling if enabled
        if(scalingOpts_.enableAutoScaling) {
            applyScaling(mesh, scalingOpts_);
        }
        
        // Finalize mesh
        mesh.finalize();
        LOG_INFO("Successfully loaded STL with " << mesh.getNumTriangles() << " triangles");
    } else {
        LOG_ERROR("Failed to read STL file");
    }
    
    return success;
}
//*************************************************************************************************

//*************************************************************************************************
bool STLReader::isBinarySTL(const std::string& filename)
{
    std::ifstream file(filename, std::ios::binary);
    if(!file.is_open()) {
        return false;
    }
    
    // Read first line
    std::string line;
    std::getline(file, line);
    file.close();
    
    // ASCII STL files start with "solid"
    std::transform(line.begin(), line.end(), line.begin(), ::tolower);
    return (line.find("solid") != 0);
}
//*************************************************************************************************

//*************************************************************************************************
bool STLReader::readASCII(const std::string& filename, TriangleMesh& mesh)
{
    std::ifstream file(filename);
    if(!file.is_open()) {
        LOG_ERROR("Failed to open ASCII STL file: " << filename);
        return false;
    }
    
    std::string line;
    std::string keyword;
    
    // Skip header line
    std::getline(file, line);
    
    int triangleCount = 0;
    
    while(std::getline(file, line)) {
        std::istringstream iss(line);
        iss >> keyword;
        
        if(keyword == "facet") {
            Vector3<Real> normal;
            Vector3<Real> vertices[3];
            
            // Read normal
            iss >> keyword; // "normal"
            iss >> normal[0] >> normal[1] >> normal[2];
            
            // Read "outer loop"
            std::getline(file, line);
            
            // Read three vertices
            for(int i = 0; i < 3; ++i) {
                std::getline(file, line);
                std::istringstream vss(line);
                vss >> keyword; // "vertex"
                vss >> vertices[i][0] >> vertices[i][1] >> vertices[i][2];
            }
            
            // Read "endloop"
            std::getline(file, line);
            
            // Read "endfacet"
            std::getline(file, line);
            
            // Add triangle to mesh
            mesh.addTriangle(vertices[0], vertices[1], vertices[2]);
            triangleCount++;
            
            if(triangleCount % 10000 == 0) {
                LOG_INFO("Read " << triangleCount << " triangles...");
            }
        }
        else if(keyword == "endsolid") {
            break;
        }
    }
    
    file.close();
    return (triangleCount > 0);
}
//*************************************************************************************************

//*************************************************************************************************
bool STLReader::readBinary(const std::string& filename, TriangleMesh& mesh)
{
    std::ifstream file(filename, std::ios::binary);
    if(!file.is_open()) {
        LOG_ERROR("Failed to open binary STL file: " << filename);
        return false;
    }
    
    // Read 80-byte header (ignore)
    char header[80];
    file.read(header, 80);
    
    // Read number of triangles
    char buffer[4];
    file.read(buffer, 4);
    unsigned int numTriangles = parseUInt32(buffer);
    
    LOG_INFO("Binary STL contains " << numTriangles << " triangles");
    
    // Pre-allocate space
    // mesh.reserve(numTriangles);
    
    // Read triangles
    for(unsigned int i = 0; i < numTriangles; ++i) {
        // Each triangle is 50 bytes:
        // - 12 bytes: normal (3 floats)
        // - 36 bytes: vertices (9 floats)
        // - 2 bytes: attribute byte count
        
        char triData[50];
        file.read(triData, 50);
        
        // Parse normal (currently unused, we compute our own)
        Vector3<Real> normal;
        normal[0] = parseFloat(triData + 0);
        normal[1] = parseFloat(triData + 4);
        normal[2] = parseFloat(triData + 8);
        
        // Parse vertices
        Vector3<Real> vertices[3];
        for(int v = 0; v < 3; ++v) {
            int offset = 12 + v * 12;
            vertices[v][0] = parseFloat(triData + offset + 0);
            vertices[v][1] = parseFloat(triData + offset + 4);
            vertices[v][2] = parseFloat(triData + offset + 8);
        }
        
        // Add triangle to mesh
        mesh.addTriangle(vertices[0], vertices[1], vertices[2]);
        
        if((i + 1) % 10000 == 0) {
            LOG_INFO("Read " << (i + 1) << " triangles...");
        }
    }
    
    file.close();
    return true;
}
//*************************************************************************************************

//*************************************************************************************************
float STLReader::parseFloat(const char* buffer)
{
    // STL uses little-endian floats
    float value;
    std::memcpy(&value, buffer, sizeof(float));
    return value;
}
//*************************************************************************************************

//*************************************************************************************************
unsigned int STLReader::parseUInt32(const char* buffer)
{
    // STL uses little-endian integers
    unsigned int value;
    std::memcpy(&value, buffer, sizeof(unsigned int));
    return value;
}
//*************************************************************************************************

//=================================================================================================
//
//  ENHANCED READER METHODS
//
//=================================================================================================

//*************************************************************************************************
void STLReader::setPreprocessingOptions(const PreprocessingOptions& options)
{
    preprocessingOpts_ = options;
}
//*************************************************************************************************

//*************************************************************************************************
void STLReader::setScalingOptions(const ScalingOptions& options)
{
    scalingOpts_ = options;
}
//*************************************************************************************************

//*************************************************************************************************
void STLReader::configureFromParameters(const std::map<std::string, std::string>& params)
{
    // Configure preprocessing options
    auto it = params.find("reComputeTheNormals");
    if(it != params.end()) {
        preprocessingOpts_.computeNormals = (it->second == "1" || it->second == "true");
    }
    
    it = params.find("checkNormConsistancy"); // Note: typo preserved for compatibility
    if(it != params.end()) {
        preprocessingOpts_.checkNormalConsistency = (it->second == "1" || it->second == "true");
    }
    
    it = params.find("centerMesh");
    if(it != params.end()) {
        preprocessingOpts_.centerMesh = (it->second == "1" || it->second == "true");
    }
    
    it = params.find("removeDuplicateVertices");
    if(it != params.end()) {
        preprocessingOpts_.removeDuplicateVertices = (it->second == "1" || it->second == "true");
    }
    
    // Configure scaling options
    it = params.find("enableAutoScaling");
    if(it != params.end()) {
        scalingOpts_.enableAutoScaling = (it->second == "1" || it->second == "true");
    }
    
    it = params.find("characteristicLength");
    if(it != params.end()) {
        scalingOpts_.characteristicLength = std::stod(it->second);
    }
    
    it = params.find("targetRegion");
    if(it != params.end()) {
        // Parse comma-separated values: "xmin,ymin,zmin,xmax,ymax,zmax"
        std::stringstream ss(it->second);
        std::string token;
        scalingOpts_.targetRegion.clear();
        while(std::getline(ss, token, ',')) {
            scalingOpts_.targetRegion.push_back(std::stod(token));
        }
    }
}
//*************************************************************************************************

//*************************************************************************************************
bool STLReader::readWithOptions(const std::string& filename, 
                                TriangleMesh& mesh,
                                const PreprocessingOptions& preprocessOpts,
                                const ScalingOptions& scalingOpts)
{
    // Temporarily store current options
    PreprocessingOptions oldPreprocessOpts = preprocessingOpts_;
    ScalingOptions oldScalingOpts = scalingOpts_;
    
    // Set new options
    preprocessingOpts_ = preprocessOpts;
    scalingOpts_ = scalingOpts;
    
    // Read with new options
    bool success = read(filename, mesh);
    
    // Restore original options
    preprocessingOpts_ = oldPreprocessOpts;
    scalingOpts_ = oldScalingOpts;
    
    return success;
}
//*************************************************************************************************

//*************************************************************************************************
std::string STLReader::analyzeSTL(const std::string& filename)
{
    std::stringstream report;
    report << "STL File Analysis: " << filename << "\n";
    report << "==========================================\n";
    
    // Check if file exists and get size
    std::ifstream file(filename, std::ios::binary | std::ios::ate);
    if(!file.is_open()) {
        report << "ERROR: Cannot open file\n";
        return report.str();
    }
    
    std::streamsize fileSize = file.tellg();
    report << "File size: " << fileSize << " bytes\n";
    file.seekg(0, std::ios::beg);
    
    // Determine format
    bool isBinary = isBinarySTL(filename);
    report << "Format: " << (isBinary ? "Binary" : "ASCII") << "\n";
    
    if(isBinary) {
        // Read binary header
        char header[80];
        file.read(header, 80);
        
        // Read number of triangles
        unsigned int numTriangles;
        file.read(reinterpret_cast<char*>(&numTriangles), 4);
        report << "Number of triangles: " << numTriangles << "\n";
        
        // Estimate memory usage
        size_t estimatedMemory = numTriangles * sizeof(Triangle) + numTriangles * 3 * sizeof(Vector3<Real>);
        report << "Estimated memory usage: " << estimatedMemory / (1024*1024) << " MB\n";
    } else {
        // For ASCII, count triangles by reading file
        file.close();
        std::ifstream asciiFile(filename);
        std::string line;
        int triangleCount = 0;
        
        while(std::getline(asciiFile, line)) {
            std::transform(line.begin(), line.end(), line.begin(), ::tolower);
            if(line.find("facet normal") != std::string::npos) {
                triangleCount++;
            }
        }
        
        report << "Number of triangles: " << triangleCount << "\n";
        size_t estimatedMemory = triangleCount * sizeof(Triangle) + triangleCount * 3 * sizeof(Vector3<Real>);
        report << "Estimated memory usage: " << estimatedMemory / (1024*1024) << " MB\n";
    }
    
    return report.str();
}
//*************************************************************************************************

//*************************************************************************************************
std::string STLReader::getMeshStatistics(const TriangleMesh& mesh) const
{
    std::stringstream stats;
    stats << std::fixed << std::setprecision(6);
    
    stats << "Mesh Statistics:\n";
    stats << "================\n";
    stats << "Number of triangles: " << mesh.getNumTriangles() << "\n";
    stats << "Number of vertices: " << mesh.getNumVertices() << "\n";
    stats << "Total surface area: " << mesh.getTotalArea() << "\n";
    stats << "Volume: " << mesh.getVolume() << "\n";
    
    Vector3<Real> bboxMin = mesh.getBoundingBoxMin();
    Vector3<Real> bboxMax = mesh.getBoundingBoxMax();
    Vector3<Real> size = bboxMax - bboxMin;
    
    stats << "Bounding box:\n";
    stats << "  Min: [" << bboxMin[0] << ", " << bboxMin[1] << ", " << bboxMin[2] << "]\n";
    stats << "  Max: [" << bboxMax[0] << ", " << bboxMax[1] << ", " << bboxMax[2] << "]\n";
    stats << "  Size: [" << size[0] << ", " << size[1] << ", " << size[2] << "]\n";
    
    if(mesh.isScaled()) {
        stats << "\nScaling Information:\n";
        stats << mesh.getScalingInfo() << "\n";
    }
    
    return stats.str();
}
//*************************************************************************************************

//=================================================================================================
//
//  PREPROCESSING METHODS
//
//=================================================================================================

//*************************************************************************************************
void STLReader::applyPreprocessing(TriangleMesh& mesh, const PreprocessingOptions& options)
{
    std::stringstream report;
    report << "Preprocessing Report:\n";
    report << "====================\n";
    
    if(options.computeNormals) {
        computeNormals(mesh);
        report << "✓ Recomputed triangle normals\n";
    }
    
    if(options.removeDuplicateVertices) {
        int removed = removeDuplicateVertices(mesh);
        report << "✓ Removed " << removed << " duplicate vertices\n";
    }
    
    if(options.checkNormalConsistency) {
        int flipped = fixNormalConsistency(mesh);
        report << "✓ Fixed " << flipped << " inconsistent normals\n";
    }
    
    if(options.validateGeometry) {
        int issues = validateGeometry(mesh, options.degenerateThreshold);
        report << "✓ Found " << issues << " geometry issues\n";
    }
    
    if(options.centerMesh) {
        Vector3<Real> center = (mesh.getBoundingBoxMin() + mesh.getBoundingBoxMax()) * 0.5;
        mesh.centerAt(Vector3<Real>(0,0,0));
        report << "✓ Centered mesh (was at [" << center[0] << ", " << center[1] << ", " << center[2] << "])\n";
    }
    
    preprocessingReport_ = report.str();
    
    if(options.verbose) {
        LOG_INFO(preprocessingReport_);
    }
}
//*************************************************************************************************

//*************************************************************************************************
void STLReader::computeNormals(TriangleMesh& mesh)
{
    for(Uint i = 0; i < mesh.getNumTriangles(); ++i) {
        Triangle& tri = mesh.getTriangle(i);
        tri.computeProperties();
    }
}
//*************************************************************************************************

//*************************************************************************************************
int STLReader::fixNormalConsistency(TriangleMesh& mesh)
{
    // Simple heuristic: ensure normals point outward by checking against centroid
    Vector3<Real> centroid = (mesh.getBoundingBoxMin() + mesh.getBoundingBoxMax()) * 0.5;
    int flipped = 0;
    
    for(Uint i = 0; i < mesh.getNumTriangles(); ++i) {
        Triangle& tri = mesh.getTriangle(i);
        
        // Vector from centroid to triangle centroid
        Vector3<Real> toTriangle = tri.centroid - centroid;
        
        // If normal points inward (negative dot product), flip it
        if(tri.normal.dot(toTriangle) < 0) {
            tri.normal = tri.normal * (-1.0);
            
            // Also swap vertex order to maintain consistency
            Vector3<Real> temp = tri.vertices[1];
            tri.vertices[1] = tri.vertices[2];
            tri.vertices[2] = temp;
            
            flipped++;
        }
    }
    
    return flipped;
}
//*************************************************************************************************

//*************************************************************************************************
int STLReader::removeDuplicateVertices(TriangleMesh& mesh)
{
    const Real epsilon = 1e-10;
    std::vector<Vector3<Real>> uniqueVertices;
    std::unordered_map<int, int> vertexMap; // old index -> new index
    
    // Find unique vertices
    for(Uint i = 0; i < mesh.getNumVertices(); ++i) {
        const Vector3<Real>& vertex = mesh.getVertex(i);
        bool found = false;
        
        for(Uint j = 0; j < uniqueVertices.size(); ++j) {
            Vector3<Real> diff = vertex - uniqueVertices[j];
            if(diff.length() < epsilon) {
                vertexMap[i] = j;
                found = true;
                break;
            }
        }
        
        if(!found) {
            vertexMap[i] = uniqueVertices.size();
            uniqueVertices.push_back(vertex);
        }
    }
    
    int removed = mesh.getNumVertices() - uniqueVertices.size();
    
    // Note: This is a simplified implementation
    // A complete implementation would rebuild the triangle mesh with new vertex indices
    
    return removed;
}
//*************************************************************************************************

//*************************************************************************************************
int STLReader::validateGeometry(TriangleMesh& mesh, Real threshold)
{
    int issues = 0;
    
    for(Uint i = 0; i < mesh.getNumTriangles(); ++i) {
        const Triangle& tri = mesh.getTriangle(i);
        
        // Check for degenerate triangles (zero area)
        if(tri.area < threshold) {
            issues++;
            continue;
        }
        
        // Check for very small edges
        for(int j = 0; j < 3; ++j) {
            int k = (j + 1) % 3;
            Vector3<Real> edge = tri.vertices[k] - tri.vertices[j];
            if(edge.length() < threshold) {
                issues++;
                break;
            }
        }
    }
    
    return issues;
}
//*************************************************************************************************

//*************************************************************************************************
void STLReader::applyScaling(TriangleMesh& mesh, const ScalingOptions& options)
{
    if(!options.converter) {
        LOG_WARNING("No units converter provided for scaling");
        return;
    }
    
    Vector3<Real> bboxMin = mesh.getBoundingBoxMin();
    Vector3<Real> bboxMax = mesh.getBoundingBoxMax();
    
    if(options.setPhysicalDimensions) {
        mesh.setPhysicalDimensions(bboxMin, bboxMax);
    }
    
    std::vector<Real> scalingParams;
    
    if(!options.targetRegion.empty() && options.targetRegion.size() >= 6) {
        // Scale to fit target region
        scalingParams = options.converter->calculateMeshScaling(
            bboxMin, bboxMax, options.targetRegion, options.mode);
    } else {
        // Scale based on characteristic length
        Vector3<Real> size = bboxMax - bboxMin;
        Real currentLength = std::max({size[0], size[1], size[2]});
        Real scaleFactor = options.characteristicLength / currentLength;
        
        Vector3<Real> center = (bboxMin + bboxMax) * 0.5;
        Vector3<Real> offset = options.physicalPosition - center * scaleFactor;
        
        scalingParams = {scaleFactor, offset[0], offset[1], offset[2]};
    }
    
    if(options.converter->validateScaling(scalingParams)) {
        mesh.applyPhysicalToLatticeScaling(scalingParams);
        LOG_INFO("Applied scaling to mesh: " << mesh.getScalingInfo());
    } else {
        LOG_WARNING("Scaling parameters failed validation - skipping scaling");
    }
}
//*************************************************************************************************

} // namespace curvedboundary
} // namespace walberla