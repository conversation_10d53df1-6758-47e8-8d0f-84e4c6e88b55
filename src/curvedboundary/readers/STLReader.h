//=================================================================================================
/*!
//  \file STLReader.h
//  \brief STL file reader for triangle meshes
*/
//=================================================================================================

#ifndef _WALBERLA_STL_READER_H
#define _WALBERLA_STL_READER_H

#include "../mesh/TriangleMesh.h"
#include "../mesh/PhysicalUnitsConverter.h"
#include <string>
#include <fstream>
#include <map>

namespace walberla {
namespace curvedboundary {

//=================================================================================================
//
//  STL READER CLASS
//
//=================================================================================================

/*!\brief Reader for STL (STereoLithography) files with preprocessing and scaling
//
// This class reads both ASCII and binary STL files, converts them to TriangleMesh 
// objects, and provides advanced preprocessing options including scaling from 
// physical to lattice units, mesh repair, and geometry validation.
*/
class STLReader
{
public:
    //**Type definitions***************************************************************************
    struct PreprocessingOptions {
        bool computeNormals = true;         //!< Recompute triangle normals
        bool checkNormalConsistency = true; //!< Check and fix normal orientation
        bool removeDuplicateVertices = true;//!< Remove duplicate vertices
        bool repairMesh = false;            //!< Basic mesh repair
        bool centerMesh = false;            //!< Center mesh at origin
        bool validateGeometry = true;       //!< Check for degenerate triangles
        Real degenerateThreshold = 1e-10;  //!< Threshold for degenerate triangles
        bool verbose = false;               //!< Print detailed preprocessing info
    };
    
    struct ScalingOptions {
        bool enableAutoScaling = false;                           //!< Enable automatic scaling
        PhysicalUnitsConverter::ScalingMode mode = PhysicalUnitsConverter::ScalingMode::PRESERVE_ASPECT;
        std::vector<Real> targetRegion;                          //!< Target lattice region [xmin,ymin,zmin,xmax,ymax,zmax]
        Real characteristicLength = 1.0;                         //!< Physical characteristic length (m)
        Vector3<Real> physicalPosition = Vector3<Real>(0,0,0);   //!< Physical position (m)
        bool setPhysicalDimensions = false;                      //!< Store physical dimensions
        std::shared_ptr<PhysicalUnitsConverter> converter;       //!< Units converter (optional)
    };
    
    //**Constructor & Destructor*******************************************************************
    STLReader();
    ~STLReader() = default;
    
    //**Configuration******************************************************************************
    
    /*!\brief Set preprocessing options
    //
    // \param options  Preprocessing configuration
    */
    void setPreprocessingOptions(const PreprocessingOptions& options);
    
    /*!\brief Set scaling options
    //
    // \param options  Scaling configuration
    */
    void setScalingOptions(const ScalingOptions& options);
    
    /*!\brief Configure from parameter map
    //
    // \param params  Parameter map (e.g., from parameter file)
    */
    void configureFromParameters(const std::map<std::string, std::string>& params);
    
    //**Reading functions**************************************************************************
    
    /*!\brief Read STL file and populate mesh
    //
    // \param filename  Path to STL file
    // \param mesh      Triangle mesh to populate
    // \return True if successful
    */
    bool read(const std::string& filename, TriangleMesh& mesh);
    
    /*!\brief Read STL file with custom preprocessing and scaling
    //
    // \param filename       Path to STL file
    // \param mesh           Triangle mesh to populate
    // \param preprocessOpts Preprocessing options
    // \param scalingOpts    Scaling options
    // \return True if successful
    */
    bool readWithOptions(const std::string& filename, 
                        TriangleMesh& mesh,
                        const PreprocessingOptions& preprocessOpts,
                        const ScalingOptions& scalingOpts);
    
    //**Analysis and validation********************************************************************
    
    /*!\brief Analyze STL file without loading full mesh
    //
    // \param filename  Path to STL file
    // \return Analysis report
    */
    std::string analyzeSTL(const std::string& filename);
    
    /*!\brief Get last preprocessing report
    */
    const std::string& getPreprocessingReport() const { return preprocessingReport_; }
    
    /*!\brief Get mesh statistics
    //
    // \param mesh  Triangle mesh to analyze
    // \return Statistics string
    */
    std::string getMeshStatistics(const TriangleMesh& mesh) const;
    
private:
    //**Private reading functions******************************************************************
    
    /*!\brief Check if file is binary STL
    //
    // \param filename  Path to STL file
    // \return True if binary format
    */
    bool isBinarySTL(const std::string& filename);
    
    /*!\brief Read ASCII STL file
    //
    // \param filename  Path to STL file
    // \param mesh      Triangle mesh to populate
    // \return True if successful
    */
    bool readASCII(const std::string& filename, TriangleMesh& mesh);
    
    /*!\brief Read binary STL file
    //
    // \param filename  Path to STL file
    // \param mesh      Triangle mesh to populate
    // \return True if successful
    */
    bool readBinary(const std::string& filename, TriangleMesh& mesh);
    
    /*!\brief Parse a float from binary data
    //
    // \param buffer  Pointer to binary data
    // \return Parsed float value
    */
    float parseFloat(const char* buffer);
    
    /*!\brief Parse an unsigned int from binary data
    //
    // \param buffer  Pointer to binary data
    // \return Parsed unsigned int value
    */
    unsigned int parseUInt32(const char* buffer);
    
    //**Preprocessing functions********************************************************************
    
    /*!\brief Apply preprocessing to loaded mesh
    //
    // \param mesh     Triangle mesh to preprocess
    // \param options  Preprocessing options
    */
    void applyPreprocessing(TriangleMesh& mesh, const PreprocessingOptions& options);
    
    /*!\brief Compute and fix triangle normals
    //
    // \param mesh  Triangle mesh
    */
    void computeNormals(TriangleMesh& mesh);
    
    /*!\brief Check and fix normal consistency
    //
    // \param mesh  Triangle mesh
    // \return Number of normals flipped
    */
    int fixNormalConsistency(TriangleMesh& mesh);
    
    /*!\brief Remove duplicate vertices
    //
    // \param mesh  Triangle mesh
    // \return Number of vertices removed
    */
    int removeDuplicateVertices(TriangleMesh& mesh);
    
    /*!\brief Validate mesh geometry
    //
    // \param mesh       Triangle mesh
    // \param threshold  Threshold for degenerate triangles
    // \return Number of issues found
    */
    int validateGeometry(TriangleMesh& mesh, Real threshold);
    
    /*!\brief Apply scaling to mesh
    //
    // \param mesh     Triangle mesh
    // \param options  Scaling options
    */
    void applyScaling(TriangleMesh& mesh, const ScalingOptions& options);
    
    //**Member variables***************************************************************************
    
    PreprocessingOptions preprocessingOpts_;  //!< Current preprocessing options
    ScalingOptions scalingOpts_;              //!< Current scaling options
    std::string preprocessingReport_;         //!< Last preprocessing report
};

} // namespace curvedboundary
} // namespace walberla

#endif // _WALBERLA_STL_READER_H