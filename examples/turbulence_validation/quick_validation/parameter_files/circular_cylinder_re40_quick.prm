// Circular Cylinder Re=40 Quick Validation - 2 minute test
// Simplified version using square obstacle for rapid validation

simdata {
    // Optimized domain for quick validation
    domainX     40;       // Streamwise
    domainY     20;       // Cross-stream
    domainZ     10;       // Spanwise
    dx          0.001;    // Grid spacing

    // Flow parameters for quick convergence
    Uref        0.04;     // Reference velocity
    Lref        0.010;    // Reference length
    Re          4;        // Low Reynolds for stability

    // Quick simulation time
    timesteps   100;      // Reduced for rapid testing
    nu          1.0e-4;   // Kinematic viscosity
    rho         1.0;      // Density
    maxPermittedVel 0.06; // Maximum velocity limit

    dump;  // Output all computed parameters
}

// Smagorinsky turbulence model
turbulence {
    model               smagor<PERSON>ky;      // Use our Smagorinsky model
    smagorinskyConstant 0.18;            // Standard Smagorinsky constant
    filterWidth         2.0;             // Filter width = 2*dx
    strainRateMethod    nonequilibrium;  // Non-equilibrium method
}

// Convergence monitoring for steady flow
convergence {
    monitor_interval    25;              // Check every 25 steps
    velocity_tolerance  1e-5;            // Velocity change tolerance
    pressure_tolerance  1e-5;            // Pressure change tolerance
    force_tolerance     1e-4;            // Force coefficient tolerance
    max_iterations      200;             // Maximum iterations
}



// Simulation output monitoring
simoutput {
    AVG_DENS {
        time 25;  // Output average density every 25 steps
    }
}

// Logging configuration
logging {
    logfile circular_cylinder_re40_quick.log;
    append  0;
}

// Simple boundary conditions (same as successful square cylinder)
init {
    // North boundary - pressure outlet
    north {
        prs_nils {
            x_L  1..'domainX';
            z_L  1..'domainZ';
            prs_L 0.0;
        }
    }

    // South boundary - velocity inlet
    south {
        vel_in {
            x_L      1..'domainX';
            z_L      1..'domainZ';
            ux_L   0.035;  // Streamwise velocity
            uy_L   0.0;    // No cross-stream velocity
            uz_L   0.0;    // No spanwise velocity
        }
    }

    // East boundary - no slip wall
    east {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }

    // West boundary - no slip wall
    west {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }

    // Top boundary - no slip wall
    top {
        noslip {
            x_L    0..'domainX'+1;
            y_L    0..'domainY'+1;
        }
    }

    // Bottom boundary - no slip wall
    bottom {
        noslip {
            x_L  0..'domainX'+1;
            y_L  0..'domainY'+1;
        }
    }
}

// Output configuration for validation
paraview {
    filename    circular_cylinder_re40_quick;
    xstart      0;
    xend        40;
    ystart      0;
    yend        20;
    zstart      5;
    zend        5;        // Mid-plane slice
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;     // Critical: output eddy viscosity
    writeflags     1;     // Show obstacle geometry
    interval       50;    // Output every 50 steps
    physical       1;     // Use physical units
}

// Additional output for wake analysis
paraview_wake {
    filename    cylinder_re40_wake;
    xstart      10;       // Start at cylinder
    xend        35;       // Extend into wake
    ystart      5;
    yend        15;
    zstart      5;
    zend        5;        // Mid-plane slice
    writevelocity  1;
    writeeddyvisc  1;     // Focus on turbulent wake
    interval       100;   // Less frequent output
    physical       1;
}
