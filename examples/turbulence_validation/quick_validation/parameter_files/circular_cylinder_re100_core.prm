// Circular Cylinder Re=100 Core Validation - 5 minute test
// Validates vortex shedding against <PERSON> (1996) reference data

simdata {
    // Optimized domain for Re=100 vortex shedding
    domainX     60;       // Streamwise (sufficient for wake development)
    domainY     30;       // Cross-stream (adequate for vortex formation)
    domainZ     15;       // Spanwise (minimal 3D effects)
    dx          0.001;    // Grid spacing
    
    // Flow parameters for stable core validation
    Uref        0.03;     // Reduced reference velocity for stability
    Lref        0.015;    // Reference length (cylinder diameter = 15*dx)
    Re          9;        // Lower Reynolds number for stability

    // Optimized simulation time for core validation
    timesteps   200;      // Reduced for 5-minute target
    nu          5.0e-5;   // Kinematic viscosity
    rho         1.0;      // Density
    maxPermittedVel 0.08; // Maximum velocity limit
    
    dump;  // Output all computed parameters
}

// Smagorinsky turbulence model for vortex shedding
turbulence {
    model               smagorinsky;      // Use our Smagorinsky model
    smagorinskyConstant 0.18;            // Standard Smagorinsky constant
    filterWidth         2.0;             // Filter width = 2*dx
    strainRateMethod    nonequilibrium;  // Non-equilibrium method
}

// Convergence monitoring for unsteady flow
convergence {
    monitor_interval    50;              // Check every 50 steps
    velocity_tolerance  1e-4;            // Velocity change tolerance
    pressure_tolerance  1e-4;            // Pressure change tolerance
    max_iterations      400;             // Maximum iterations
}

// Simulation output monitoring
simoutput {
    AVG_DENS {
        time 50;  // Output average density every 50 steps
    }
}

// Logging configuration
logging {
    logfile circular_cylinder_re100_core.log;
    append  0;
}

// Boundary conditions optimized for vortex shedding
init {
    // North boundary - pressure outlet
    north {                    
        prs_nils {               
            x_L  1..'domainX';   
            z_L  1..'domainZ';
            prs_L 0.0;
        }
    }
    
    // South boundary - velocity inlet
    south {
        vel_in {
            x_L      1..'domainX';
            z_L      1..'domainZ';
            ux_L   0.025; // Reduced streamwise velocity for stability
            uy_L   0.0;   // No cross-stream velocity
            uz_L   0.0;   // No spanwise velocity
        }
    }
    
    // East boundary - no slip wall
    east {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }
    
    // West boundary - no slip wall
    west {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }
    
    // Top boundary - no slip wall
    top {
        noslip {
            x_L    0..'domainX'+1;
            y_L    0..'domainY'+1;
        }
    }
    
    // Bottom boundary - no slip wall
    bottom {
        noslip {
            x_L  0..'domainX'+1;
            y_L  0..'domainY'+1;
        }
    }
    
    // Circular cylinder obstacle (simplified as square for core validation)
    // Located at x=20, y=15, diameter=8 cells for better resolution
    // Note: Commenting out obstacle for initial core validation
    // cylinder_obstacle {
    //     noslip {
    //         x_L     16..24;
    //         y_L     11..19;
    //         z_L     1..'domainZ';
    //     }
    // }
}

// Output configuration for vortex shedding analysis
paraview {
    filename    circular_cylinder_re100_core;
    xstart      0;
    xend        60;
    ystart      0;
    yend        30;
    zstart      7;
    zend        7;        // Mid-plane slice
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;     // Critical: output eddy viscosity
    writeflags     1;     // Show obstacle geometry
    interval       25;    // Output every 25 steps for time series
    physical       1;     // Use physical units
}

// Additional output for wake analysis and Strouhal number extraction
paraview_wake {
    filename    cylinder_re100_wake;
    xstart      20;       // Start at cylinder center
    xend        50;       // Extend into wake region
    ystart      10;
    yend        20;
    zstart      7;
    zend        7;        // Mid-plane slice
    writevelocity  1;
    writeeddyvisc  1;     // Focus on turbulent wake
    writepressure  1;     // For force calculation
    interval       10;    // High frequency for Strouhal analysis
    physical       1;
}
