// Quick Channel Validation - 30 second test
// Validates basic turbulence model activation and flow development

simdata {
    // Minimal domain for rapid testing
    domainX     20;       // Streamwise
    domainY     10;       // Cross-stream
    domainZ     10;       // Spanwise
    dx          0.001;    // Grid spacing
    
    // Flow parameters for quick convergence
    Uref        0.05;     // Reference velocity
    Lref        0.010;    // Reference length (channel height)
    Re          5;        // Low Reynolds number for stability
    
    // Minimal simulation time
    timesteps   50;       // Very short run
    nu          1.0e-4;   // Kinematic viscosity
    rho         1.0;      // Density
    maxPermittedVel 0.08; // Maximum velocity limit
    
    dump;  // Output all computed parameters
}

// Smagorinsky turbulence model
turbulence {
    model               smagor<PERSON>ky;      // Use our Smagorinsky model
    smagorinskyConstant 0.18;            // Standard Smagorinsky constant
    filterWidth         2.0;             // Filter width = 2*dx
    strainRateMethod    nonequilibrium;  // Non-equilibrium method
}

// Convergence monitoring
convergence {
    monitor_interval    10;              // Check every 10 steps
    velocity_tolerance  1e-6;            // Velocity change tolerance
    pressure_tolerance  1e-6;            // Pressure change tolerance
    max_iterations      50;              // Maximum iterations
}

// Simulation output monitoring
simoutput {
    AVG_DENS {
        time 10;  // Output average density every 10 steps
    }
}

// Logging configuration
logging {
    logfile quick_channel_validation.log;
    append  0;
}

// Simple channel boundary conditions
init {
    // North boundary - pressure outlet
    north {                    
        prs_nils {               
            x_L  1..'domainX';   
            z_L  1..'domainZ';
            prs_L 0.0;
        }
    }
    
    // South boundary - velocity inlet
    south {
        vel_in {
            x_L      1..'domainX';
            z_L      1..'domainZ';
            ux_L   0.04;  // Streamwise velocity
            uy_L   0.0;   // No cross-stream velocity
            uz_L   0.0;   // No spanwise velocity
        }
    }
    
    // East boundary - no slip wall
    east {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }
    
    // West boundary - no slip wall
    west {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }
    
    // Top boundary - no slip wall
    top {
        noslip {
            x_L    0..'domainX'+1;
            y_L    0..'domainY'+1;
        }
    }
    
    // Bottom boundary - no slip wall
    bottom {
        noslip {
            x_L  0..'domainX'+1;
            y_L  0..'domainY'+1;
        }
    }
}

// Minimal output for validation
paraview {
    filename    quick_channel_validation;
    xstart      0;
    xend        20;
    ystart      0;
    yend        10;
    zstart      5;
    zend        5;        // Single plane
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;     // Critical: output eddy viscosity
    interval       25;    // Output only at end
    physical       1;     // Use physical units
}
