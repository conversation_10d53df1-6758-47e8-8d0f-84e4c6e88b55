// Square Cylinder Turbulence Test - Smagorinsky Model
// Tests turbulence models with flow separation and wake formation

simdata {
    // Domain size - channel with square obstacle
    domainX     80;       // Streamwise (sufficient for wake development)
    domainY     40;       // Cross-stream (wide channel)
    domainZ     20;       // Spanwise (3D effects)
    dx          0.001;    // Grid spacing
    
    // Flow parameters
    Uref        0.05;     // Reference velocity
    Lref        0.008;    // Reference length (cylinder width = 8*dx)
    Re          4;        // Low Reynolds number for stability

    // Simulation control
    timesteps   300;      // Sufficient for wake development
    nu          1.0e-4;   // Kinematic viscosity
    rho         1.0;      // Density
    maxPermittedVel 0.1;  // Maximum velocity limit
    
    dump;  // Output all computed parameters
}

// Smagorinsky turbulence model
turbulence {
    model               smagorinsky;      // Use our Smagorinsky model
    smagorinskyConstant 0.18;            // Standard Smagorinsky constant
    filterWidth         2.0;             // Filter width = 2*dx
    strainRateMethod    nonequilibrium;  // Non-equilibrium method
}

// Simulation output monitoring
simoutput {
    AVG_DENS {
        time 25;  // Output average density every 25 steps
    }
}

// Logging configuration
logging {
    logfile square_cylinder_smagorinsky.log;
    append  0;
}

// Channel flow with square cylinder obstacle
init {
    // Inlet (west) - uniform velocity profile
    west {
        vel_in {
            y_L      1..'domainY'-1;
            z_L      1..'domainZ'-1;
            ux_L   0.05;  // Inlet velocity
            uy_L   0.0;
            uz_L   0.0;
        }
    }
    
    // Outlet (east) - pressure outlet
    east {
        prs_nils {
            y_L      1..'domainY'-1;
            z_L      1..'domainZ'-1;
            prs_L   0.0;  // Zero gauge pressure
        }
    }
    
    // Channel walls (north and south) - no slip
    north {
        noslip {
            x_L     0..'domainX'+1;
            z_L     1..'domainZ'-1;
        }
    }
    
    south {
        noslip {
            x_L     0..'domainX'+1;
            z_L     1..'domainZ'-1;
        }
    }
    
    // Side walls (top and bottom) - slip for 2D-like behavior
    top {
        freeslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
    
    bottom {
        freeslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
    
    // Square cylinder obstacle (8x8 cells, centered in cross-section)
    // Located at x=20-27, y=16-23, full span in z
    obstacle_square {
        noslip {
            x_L     20..27;
            y_L     16..23;
            z_L     1..'domainZ'-1;
        }
    }
}

// Output configuration for turbulence visualization
paraview {
    filename    square_cylinder_smagorinsky;
    xstart      0;
    xend        80;
    ystart      0;
    yend        40;
    zstart      10;
    zend        10;       // Mid-plane slice
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;     // Critical: output eddy viscosity
    writeflags     1;     // Show obstacle geometry
    interval       25;    // Output every 25 steps
    physical       1;     // Use physical units
}

// Additional slice for wake analysis
paraview_wake {
    filename    square_cylinder_wake;
    xstart      25;       // Start just downstream of cylinder
    xend        75;       // Extend into wake region
    ystart      10;
    yend        30;
    zstart      10;
    zend        10;       // Mid-plane slice
    writevelocity  1;
    writeeddyvisc  1;     // Focus on turbulent wake
    interval       50;    // Less frequent output
    physical       1;
}
