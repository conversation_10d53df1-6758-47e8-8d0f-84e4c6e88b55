// Square Cylinder Turbulence Test - Simplified Version
// Based on our successful basic validation with added obstacle

simdata {
    // Domain size - extended from basic validation
    domainX     60;       // Streamwise (extended for wake)
    domainY     30;       // Cross-stream (wider for obstacle)
    domainZ     20;       // Spanwise (same as basic validation)
    dx          0.001;    // Grid spacing
    
    // Flow parameters (same as working basic validation)
    Uref        0.05;     // Reference velocity
    Lref        0.030;    // Reference length (channel height)
    Re          15;       // Reynolds number
    
    // Simulation control
    timesteps   200;      // Sufficient for flow development
    nu          1.0e-4;   // Kinematic viscosity
    rho         1.0;      // Density
    maxPermittedVel 0.08; // Maximum velocity limit
    
    dump;  // Output all computed parameters
}

// Smagorinsky turbulence model (same as working validation)
turbulence {
    model               smagorinsky;      // Use our Smagorinsky model
    smagorinskyConstant 0.18;            // Standard Smagorinsky constant
    filterWidth         2.0;             // Filter width = 2*dx
    strainRateMethod    nonequilibrium;  // Non-equilibrium method
}

// Simulation output monitoring
simoutput {
    AVG_DENS {
        time 25;  // Output average density every 25 steps
    }
}

// Logging configuration
logging {
    logfile square_cylinder_simple.log;
    append  0;
}

// Boundary conditions using EXACT format from working basic validation
init {
    // North boundary - pressure outlet
    north {                    
        prs_nils {               
            x_L  1..'domainX';   
            z_L  1..'domainZ';
            prs_L 0.0;
        }
    }
    
    // South boundary - velocity inlet
    south {
        vel_in {
            x_L      1..'domainX';
            z_L      1..'domainZ';
            ux_L   0.04;  // Streamwise velocity
            uy_L   0.0;   // No cross-stream velocity
            uz_L   0.0;   // No spanwise velocity
        }
    }
    
    // East boundary - no slip wall
    east {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }
    
    // West boundary - no slip wall
    west {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }
    
    // Top boundary - no slip wall
    top {
        noslip {
            x_L    0..'domainX'+1;
            y_L    0..'domainY'+1;
        }
    }
    
    // Bottom boundary - no slip wall
    bottom {
        noslip {
            x_L  0..'domainX'+1;
            y_L  0..'domainY'+1;
        }
    }
    
    // Square cylinder obstacle (6x6 cells, centered)
    // Located at x=20-25, y=12-17, full span in z
    obstacle_square {
        noslip {
            x_L     20..25;
            y_L     12..17;
            z_L     1..'domainZ';
        }
    }
}

// Output configuration for turbulence visualization
paraview {
    filename    square_cylinder_simple;
    xstart      0;
    xend        60;
    ystart      0;
    yend        30;
    zstart      10;
    zend        10;       // Mid-plane slice
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;     // Critical: output eddy viscosity
    writeflags     1;     // Show obstacle geometry
    interval       25;    // Output every 25 steps
    physical       1;     // Use physical units
}
