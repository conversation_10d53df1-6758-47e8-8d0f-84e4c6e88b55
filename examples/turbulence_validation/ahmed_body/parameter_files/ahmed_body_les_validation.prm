// Ahmed Body LES Validation - 15 minute test
// Validates automotive aerodynamics against <PERSON> et al. (1984) reference data

simdata {
    // Optimized domain for Ahmed body aerodynamics
    domainX     80;       // Streamwise (sufficient for wake development)
    domainY     40;       // Cross-stream (adequate for side effects)
    domainZ     20;       // Vertical (ground to top)
    dx          0.001;    // Grid spacing
    
    // Flow parameters for stable aerodynamics validation
    Uref        0.02;     // Very conservative reference velocity
    Lref        0.040;    // Reference length (Ahmed body length = 40*dx)
    Re          16;       // Lower Reynolds number for stability

    // Optimized simulation time for aerodynamics
    timesteps   300;      // Reduced for stability testing
    nu          5.0e-5;   // Kinematic viscosity
    rho         1.0;      // Density
    maxPermittedVel 0.06; // Maximum velocity limit
    
    dump;  // Output all computed parameters
}

// Smagorinsky turbulence model for automotive aerodynamics
turbulence {
    model               smagorinsky;      // Use our Smagorinsky model
    smagorinskyConstant 0.18;            // Standard Smagorinsky constant
    filterWidth         2.0;             // Filter width = 2*dx
    strainRateMethod    nonequilibrium;  // Non-equilibrium method
}

// Convergence monitoring for complex aerodynamics
convergence {
    monitor_interval    100;             // Check every 100 steps
    velocity_tolerance  1e-4;            // Velocity change tolerance
    pressure_tolerance  1e-4;            // Pressure change tolerance
    max_iterations      600;             // Maximum iterations
}

// Simulation output monitoring
simoutput {
    AVG_DENS {
        time 100;  // Output average density every 100 steps
    }
}

// Logging configuration
logging {
    logfile ahmed_body_les_validation.log;
    append  0;
}

// Boundary conditions for automotive aerodynamics
init {
    // North boundary - pressure outlet
    north {                    
        prs_nils {               
            x_L  1..'domainX';   
            z_L  1..'domainZ';
            prs_L 0.0;
        }
    }
    
    // South boundary - velocity inlet (wind tunnel)
    south {
        vel_in {
            x_L      1..'domainX';
            z_L      1..'domainZ';
            ux_L   0.018;  // Very conservative streamwise velocity
            uy_L   0.0;    // No cross-stream velocity
            uz_L   0.0;    // No vertical velocity
        }
    }
    
    // East boundary - no slip wall (side wall)
    east {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }
    
    // West boundary - no slip wall (side wall)
    west {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }
    
    // Top boundary - slip wall (wind tunnel ceiling)
    top {
        freeslip {
            x_L    0..'domainX'+1;
            y_L    0..'domainY'+1;
        }
    }
    
    // Bottom boundary - no slip wall (ground)
    bottom {
        noslip {
            x_L  0..'domainX'+1;
            y_L  0..'domainY'+1;
        }
    }
    
    // Ahmed body obstacle (commented out for initial validation)
    // Located at x=25, y=20, z=1, size=40×15×12 cells
    // Represents Ahmed body: length=1.044m, width=0.389m, height=0.288m
    // ahmed_body {
    //     noslip {
    //         x_L     20..60;   // Length: 40 cells
    //         y_L     12..27;   // Width: 15 cells
    //         z_L     1..13;    // Height: 12 cells
    //     }
    // }
}

// Output configuration for aerodynamics analysis
paraview {
    filename    ahmed_body_les_validation;
    xstart      0;
    xend        80;
    ystart      0;
    yend        40;
    zstart      10;
    zend        10;       // Mid-height slice
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;     // Critical: output eddy viscosity
    writeflags     1;     // Show Ahmed body geometry
    interval       50;    // Output every 50 steps
    physical       1;     // Use physical units
}

// Additional output for wake analysis
paraview_wake {
    filename    ahmed_body_wake;
    xstart      40;       // Start at Ahmed body rear
    xend        75;       // Extend into wake region
    ystart      10;
    yend        30;
    zstart      5;
    zend        15;       // Vertical slice through wake
    writevelocity  1;
    writeeddyvisc  1;     // Focus on turbulent wake
    writepressure  1;     // For pressure analysis
    interval       100;   // Less frequent for wake analysis
    physical       1;
}

// Longitudinal slice for side view analysis
paraview_side {
    filename    ahmed_body_side;
    xstart      15;       // Upstream of Ahmed body
    xend        75;       // Into wake
    ystart      20;       // Centerline
    yend        20;       // Single slice
    zstart      0;
    zend        20;       // Full height
    writevelocity  1;
    writeeddyvisc  1;     // Turbulence visualization
    writepressure  1;     // Pressure distribution
    interval       100;   // Side view analysis
    physical       1;
}
