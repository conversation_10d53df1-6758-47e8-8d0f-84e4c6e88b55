// Working Turbulence Validation Test
// Uses the exact boundary condition format from working example

simdata {
    // Domain size
    domainX     40;       // Streamwise
    domainY     20;       // Cross-stream
    domainZ     20;       // Spanwise
    dx          0.001;    // Grid spacing
    
    // Flow parameters (matching working example format)
    Uref        0.05;     // Reference velocity
    Lref        0.020;    // Reference length (channel height)
    Re          10;       // Low Reynolds number for stability
    
    // Simulation control
    timesteps   150;      // Sufficient for flow development
    nu          1.0e-4;   // Kinematic viscosity
    rho         1.0;      // Density
    maxPermittedVel 0.08; // Maximum velocity limit
    
    dump;  // Output all computed parameters
}

// Test our Smagorinsky turbulence model
turbulence {
    model               sma<PERSON><PERSON>ky;      // Use our Smagorinsky model
    smagorinskyConstant 0.18;            // Standard Smagorinsky constant
    filterWidth         2.0;             // Filter width = 2*dx
    strainRateMethod    nonequilibrium;  // Non-equilibrium method
}

// Simulation output monitoring (using available features)
simoutput {
    AVG_DENS {
        time 25;  // Output average density every 25 steps
    }
}

// Logging configuration
logging {
    logfile turbulence_validation_working.log;
    append  0;
}

// Boundary conditions using EXACT format from working example
init {
    // North boundary - pressure outlet
    north {                    
        prs_nils {               
            x_L  1..'domainX';   
            z_L  1..'domainZ';
            prs_L 0.0;
        }
    }
    
    // South boundary - velocity inlet
    south {
        vel_in {
            x_L      1..'domainX';
            z_L      1..'domainZ';
            ux_L   0.04;  // Streamwise velocity
            uy_L   0.0;   // No cross-stream velocity
            uz_L   0.0;   // No spanwise velocity
        }
    }
    
    // East boundary - no slip wall
    east {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }
    
    // West boundary - no slip wall
    west {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }
    
    // Top boundary - no slip wall
    top {
        noslip {
            x_L    0..'domainX'+1;
            y_L    0..'domainY'+1;
        }
    }
    
    // Bottom boundary - no slip wall
    bottom {
        noslip {
            x_L  0..'domainX'+1;
            y_L  0..'domainY'+1;
        }
    }
}

// Output configuration for turbulence visualization
paraview {
    filename    turbulence_validation;
    xstart      0;
    xend        40;
    ystart      0;
    yend        20;
    zstart      10;
    zend        10;       // Mid-plane slice
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;     // Critical: output eddy viscosity to verify turbulence
    interval       50;    // Output every 50 steps
    physical       1;     // Use physical units
}
