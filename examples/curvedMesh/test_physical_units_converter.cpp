//=================================================================================================
/*!
//  \file test_physical_units_converter.cpp
//  \brief Test PhysicalUnitsConverter core functionality
*/
//=================================================================================================

#include <iostream>
#include <iomanip>
#include <cassert>
#include <cmath>
#include <vector>
#include <algorithm>

// Simplified types for testing
typedef double Real;
typedef unsigned int Uint;
typedef unsigned int uint_t;

template<typename T>
struct Vector3 {
    T data[3];
    Vector3() : data{0, 0, 0} {}
    Vector3(T x, T y, T z) : data{x, y, z} {}
    T& operator[](int i) { return data[i]; }
    const T& operator[](int i) const { return data[i]; }
    Vector3 operator*(T s) const { return Vector3(data[0]*s, data[1]*s, data[2]*s); }
    Vector3 operator+(const Vector3& v) const { return Vector3(data[0]+v[0], data[1]+v[1], data[2]+v[2]); }
};

// Simplified PhysicalUnitsConverter for testing
class PhysicalUnitsConverter {
public:
    enum class ScalingMode {
        MANUAL, AUTO_FIT, PRESERVE_ASPECT, TARGET_SIZE
    };

private:
    Real dx_phys_, dt_phys_;
    Vector3<Real> domainSizePhys_;
    Vector3<Uint> domainSizeLattice_;
    Real referenceLength_, referenceVelocity_;

public:
    PhysicalUnitsConverter(Real dx, Real dt, const Vector3<Real>& domainPhys)
        : dx_phys_(dx), dt_phys_(dt), domainSizePhys_(domainPhys),
          referenceLength_(1.0), referenceVelocity_(1.0) {}

    void setDomainSizes(const Vector3<Real>& phys, const Vector3<Uint>& lattice) {
        domainSizePhys_ = phys;
        domainSizeLattice_ = lattice;
    }

    void setReferenceValues(Real Lref, Real Uref) {
        referenceLength_ = Lref;
        referenceVelocity_ = Uref;
    }

    Real getDx() const { return dx_phys_; }
    Real getDt() const { return dt_phys_; }

    Real physicalToLatticeLength(Real physLength) const {
        return physLength / dx_phys_;
    }

    Real latticeToPhysicalLength(Real latticeLength) const {
        return latticeLength * dx_phys_;
    }

    Real physicalToLatticeVelocity(Real physVel) const {
        return physVel * dt_phys_ / dx_phys_;
    }

    Real latticeToPhysicalVelocity(Real latticeVel) const {
        return latticeVel * dx_phys_ / dt_phys_;
    }

    Real physicalToLatticeTime(Real physTime) const {
        return physTime / dt_phys_;
    }

    Real latticeToPhysicalTime(Real latticeTime) const {
        return latticeTime * dt_phys_;
    }

    std::vector<Real> calculateMeshScaling(
        const Vector3<Real>& meshMin, const Vector3<Real>& meshMax,
        const std::vector<Real>& targetRegion, ScalingMode mode) const {

        Vector3<Real> meshSize(meshMax[0] - meshMin[0],
                              meshMax[1] - meshMin[1],
                              meshMax[2] - meshMin[2]);

        Vector3<Real> regionSize(targetRegion[3] - targetRegion[0],
                                targetRegion[4] - targetRegion[1],
                                targetRegion[5] - targetRegion[2]);

        Vector3<Real> scaleFactors;

        switch(mode) {
            case ScalingMode::PRESERVE_ASPECT: {
                Real scale1 = regionSize[0]/meshSize[0];
                Real scale2 = regionSize[1]/meshSize[1];
                Real scale3 = regionSize[2]/meshSize[2];
                Real minScale = std::min(scale1, std::min(scale2, scale3));
                scaleFactors = Vector3<Real>(minScale, minScale, minScale);
                break;
            }
            case ScalingMode::AUTO_FIT:
                scaleFactors = Vector3<Real>(regionSize[0]/meshSize[0],
                                           regionSize[1]/meshSize[1],
                                           regionSize[2]/meshSize[2]);
                break;
            default:
                scaleFactors = Vector3<Real>(1.0, 1.0, 1.0);
        }

        // Calculate offsets to center in target region
        Vector3<Real> scaledSize(meshSize[0] * scaleFactors[0],
                                meshSize[1] * scaleFactors[1],
                                meshSize[2] * scaleFactors[2]);

        Vector3<Real> offset((targetRegion[0] + targetRegion[3])/2 - scaledSize[0]/2,
                            (targetRegion[1] + targetRegion[4])/2 - scaledSize[1]/2,
                            (targetRegion[2] + targetRegion[5])/2 - scaledSize[2]/2);

        return {scaleFactors[0], scaleFactors[1], scaleFactors[2],
                offset[0], offset[1], offset[2]};
    }

    std::vector<Real> scaleToCharacteristicLength(
        const Vector3<Real>& meshMin, const Vector3<Real>& meshMax,
        Real targetLength, Uint direction = 0) const {

        Real currentLength = meshMax[direction] - meshMin[direction];
        Real targetPhysLength = targetLength * dx_phys_;
        Real scaleFactor = targetPhysLength / currentLength;

        Vector3<Real> meshCenter((meshMin[0] + meshMax[0])/2,
                                (meshMin[1] + meshMax[1])/2,
                                (meshMin[2] + meshMax[2])/2);

        return {scaleFactor, -meshCenter[0] * scaleFactor,
                -meshCenter[1] * scaleFactor, -meshCenter[2] * scaleFactor};
    }

    bool validateParameters() const {
        return dx_phys_ > 0 && dt_phys_ > 0 && referenceLength_ > 0 && referenceVelocity_ > 0;
    }

    void printConversionSummary() const {
        std::cout << "Physical Units Converter Summary:\n";
        std::cout << "  dx_phys = " << dx_phys_ << " m\n";
        std::cout << "  dt_phys = " << dt_phys_ << " s\n";
        std::cout << "  Reference length = " << referenceLength_ << " m\n";
        std::cout << "  Reference velocity = " << referenceVelocity_ << " m/s\n";
        std::cout << "  Length scale: 1 lu = " << dx_phys_ << " m\n";
        std::cout << "  Time scale: 1 ts = " << dt_phys_ << " s\n";
        std::cout << "  Velocity scale: 1 lu/ts = " << dx_phys_/dt_phys_ << " m/s\n";
    }
};

void testBasicConversions() {
    std::cout << "\n=== Testing Basic Unit Conversions ===\n";
    
    // Typical simulation parameters
    Real dx_phys = 0.00312;  // m
    Real dt_phys = 0.0005832; // s
    Vector3<Real> domainSizePhys(0.1, 0.2, 0.08); // m
    Vector3<uint_t> domainSizeLattice(32, 64, 26);
    
    PhysicalUnitsConverter converter(dx_phys, dt_phys, domainSizePhys);
    converter.setDomainSizes(domainSizePhys, domainSizeLattice);
    converter.setReferenceValues(0.04, 0.535); // Lref=4cm, Uref=0.535 m/s
    
    // Test length conversions
    Real physLength = 0.04; // 4 cm
    Real latticeLength = converter.physicalToLatticeLength(physLength);
    Real backToPhys = converter.latticeToPhysicalLength(latticeLength);
    
    std::cout << "Physical length: " << physLength << " m\n";
    std::cout << "Lattice length: " << latticeLength << " lu\n";
    std::cout << "Back to physical: " << backToPhys << " m\n";
    
    assert(std::abs(physLength - backToPhys) < 1e-10);
    std::cout << "✓ Length conversion test passed\n";
    
    // Test velocity conversions
    Real physVel = 0.535; // m/s
    Real latticeVel = converter.physicalToLatticeVelocity(physVel);
    Real backToPhysVel = converter.latticeToPhysicalVelocity(latticeVel);
    
    std::cout << "Physical velocity: " << physVel << " m/s\n";
    std::cout << "Lattice velocity: " << latticeVel << " lu/ts\n";
    std::cout << "Back to physical: " << backToPhysVel << " m/s\n";
    
    assert(std::abs(physVel - backToPhysVel) < 1e-10);
    std::cout << "✓ Velocity conversion test passed\n";
    
    // Test time conversions
    Real physTime = 1.0; // 1 second
    Real latticeTime = converter.physicalToLatticeTime(physTime);
    Real backToPhysTime = converter.latticeToPhysicalTime(latticeTime);
    
    std::cout << "Physical time: " << physTime << " s\n";
    std::cout << "Lattice time: " << latticeTime << " ts\n";
    std::cout << "Back to physical: " << backToPhysTime << " s\n";
    
    assert(std::abs(physTime - backToPhysTime) < 1e-10);
    std::cout << "✓ Time conversion test passed\n";
}

void testScalingModes() {
    std::cout << "\n=== Testing Scaling Modes ===\n";
    
    PhysicalUnitsConverter converter(0.001, 0.0001, Vector3<Real>(0.1, 0.1, 0.1));
    converter.setDomainSizes(Vector3<Real>(0.1, 0.1, 0.1), Vector3<uint_t>(100, 100, 100));
    
    // Create a test mesh bounding box (physical units)
    Vector3<Real> meshMin(0.02, 0.03, 0.04); // 2cm, 3cm, 4cm
    Vector3<Real> meshMax(0.06, 0.07, 0.08); // 6cm, 7cm, 8cm
    
    // Target region in lattice units
    std::vector<Real> targetRegion = {20, 30, 40, 60, 70, 80}; // [xmin,ymin,zmin,xmax,ymax,zmax]
    
    // Test PRESERVE_ASPECT mode
    auto scalingParams = converter.calculateMeshScaling(
        meshMin, meshMax, targetRegion, 
        PhysicalUnitsConverter::ScalingMode::PRESERVE_ASPECT);
    
    std::cout << "PRESERVE_ASPECT scaling:\n";
    std::cout << "  Scale factors: [" << scalingParams[0] << ", " << scalingParams[1] << ", " << scalingParams[2] << "]\n";
    std::cout << "  Offsets: [" << scalingParams[3] << ", " << scalingParams[4] << ", " << scalingParams[5] << "]\n";
    
    // For PRESERVE_ASPECT, all scale factors should be equal
    assert(std::abs(scalingParams[0] - scalingParams[1]) < 1e-6);
    assert(std::abs(scalingParams[1] - scalingParams[2]) < 1e-6);
    std::cout << "✓ PRESERVE_ASPECT mode test passed\n";
    
    // Test AUTO_FIT mode
    scalingParams = converter.calculateMeshScaling(
        meshMin, meshMax, targetRegion, 
        PhysicalUnitsConverter::ScalingMode::AUTO_FIT);
    
    std::cout << "AUTO_FIT scaling:\n";
    std::cout << "  Scale factors: [" << scalingParams[0] << ", " << scalingParams[1] << ", " << scalingParams[2] << "]\n";
    std::cout << "  Offsets: [" << scalingParams[3] << ", " << scalingParams[4] << ", " << scalingParams[5] << "]\n";
    
    // For AUTO_FIT, scale factors can be different
    std::cout << "✓ AUTO_FIT mode test passed\n";
}

void testCharacteristicLengthScaling() {
    std::cout << "\n=== Testing Characteristic Length Scaling ===\n";
    
    PhysicalUnitsConverter converter(0.001, 0.0001, Vector3<Real>(0.1, 0.1, 0.1));
    
    // Create a cylinder-like mesh (diameter = 4cm)
    Vector3<Real> meshMin(-0.02, -0.02, 0.0);  // -2cm, -2cm, 0cm
    Vector3<Real> meshMax(0.02, 0.02, 0.1);    // +2cm, +2cm, 10cm
    
    Real targetLength = 26.0; // Target diameter in lattice units
    
    auto scalingParams = converter.scaleToCharacteristicLength(
        meshMin, meshMax, targetLength, 0); // Scale based on x-direction
    
    std::cout << "Characteristic length scaling:\n";
    std::cout << "  Original diameter (x): " << (meshMax[0] - meshMin[0]) << " m\n";
    std::cout << "  Target diameter: " << targetLength << " lu\n";
    std::cout << "  Scale factor: " << scalingParams[0] << "\n";
    std::cout << "  Offsets: [" << scalingParams[1] << ", " << scalingParams[2] << ", " << scalingParams[3] << "]\n";
    
    // Verify the scaling
    Real scaledDiameter = (meshMax[0] - meshMin[0]) * scalingParams[0];
    Real expectedDiameter = targetLength * converter.getDx();
    
    std::cout << "  Scaled diameter (physical): " << scaledDiameter << " m\n";
    std::cout << "  Expected diameter (physical): " << expectedDiameter << " m\n";
    
    assert(std::abs(scaledDiameter - expectedDiameter) < 1e-6);
    std::cout << "✓ Characteristic length scaling test passed\n";
}

void testValidation() {
    std::cout << "\n=== Testing Validation Methods ===\n";
    
    PhysicalUnitsConverter converter(0.001, 0.0001, Vector3<Real>(0.1, 0.1, 0.1));
    
    // Test valid parameters
    bool isValid = converter.validateParameters();
    std::cout << "Parameter validation: " << (isValid ? "VALID" : "INVALID") << "\n";
    assert(isValid);
    std::cout << "✓ Parameter validation test passed\n";
    
    // Test conversion summary
    std::cout << "\nConversion Summary:\n";
    converter.printConversionSummary();
}

int main() {
    std::cout << "Testing PhysicalUnitsConverter Core Functionality\n";
    std::cout << "================================================\n";
    
    try {
        testBasicConversions();
        testScalingModes();
        testCharacteristicLengthScaling();
        testValidation();
        
        std::cout << "\n🎉 All PhysicalUnitsConverter tests passed!\n";
        return 0;
    }
    catch(const std::exception& e) {
        std::cerr << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
}
