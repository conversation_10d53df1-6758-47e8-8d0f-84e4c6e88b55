simdata {
    domainX 100;
    domainY 50;
    domainZ 50;
    
    dx 1.0;
    dt 1.0;
    
    kinematicViscosity 0.01;
    density 1.0;
    
    referenceVelocity 0.1;
    referenceLength 26.0;
    
    reynoldsNumber 100.0;
    
    maxTimeSteps 1000;
    outputInterval 100;
    
    turbulenceModel "Smagorinsky";
    smagorinskyConstant 0.17;
}

boundaries {
    inlet {
        type "velocity";
        velocity 0.1 0.0 0.0;
        position "west";
    }
    
    outlet {
        type "pressure";
        pressure 0.0;
        position "east";
    }
    
    walls {
        type "noslip";
        position "north south top bottom";
    }
}

geometry {
    cylinder {
        center 25.0 25.0 25.0;
        radius 13.0;
        type "noslip";
    }
}

output {
    forces {
        enabled true;
        filename "forces.dat";
        interval 10;
    }
    
    fields {
        enabled true;
        format "vtk";
        interval 100;
    }
}

validation {
    case "circular_cylinder";
    expectedDragCoefficient 1.33;
    expectedStrouhalNumber 0.164;
    tolerance 0.05;
}
