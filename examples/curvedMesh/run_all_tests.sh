#!/bin/bash

#=================================================================================================
# run_all_tests.sh
# Comprehensive test runner for the enhanced mesh scaling system
#=================================================================================================

echo "=========================================="
echo "Enhanced Mesh Scaling System Test Suite"
echo "=========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counter
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_executable="$2"
    
    echo -e "${BLUE}Running: $test_name${NC}"
    echo "----------------------------------------"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if ./$test_executable; then
        echo -e "${GREEN}✓ $test_name PASSED${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ $test_name FAILED${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    echo ""
}

# Check if we're in the right directory
if [ ! -f "test_cube.stl" ]; then
    echo -e "${RED}Error: test_cube.stl not found. Please run from the correct directory.${NC}"
    exit 1
fi

echo "Compiling all tests..."
echo "======================"

# Compile all tests
echo "Compiling PhysicalUnitsConverter test..."
g++ -std=c++14 test_physical_units_converter.cpp -o test_physical_units_converter

echo "Compiling TriangleMesh scaling test..."
g++ -std=c++14 test_triangle_mesh_scaling.cpp -o test_triangle_mesh_scaling

echo "Compiling STLReader enhanced test..."
g++ -std=c++14 test_stl_reader_enhanced.cpp -o test_stl_reader_enhanced

echo "Compiling automatic scaling test..."
g++ -std=c++14 test_automatic_scaling.cpp -o test_automatic_scaling

echo "Compiling backward compatibility test..."
g++ -std=c++14 test_backward_compatibility.cpp -o test_backward_compatibility

echo "Compiling integration test..."
g++ -std=c++14 test_integration.cpp -o test_integration

echo -e "${GREEN}All tests compiled successfully!${NC}"
echo ""

# Run all tests
echo "Running Test Suite..."
echo "===================="
echo ""

run_test "PhysicalUnitsConverter Core Functionality" "test_physical_units_converter"
run_test "TriangleMesh Enhanced Scaling Methods" "test_triangle_mesh_scaling"
run_test "STLReader Enhanced with Preprocessing" "test_stl_reader_enhanced"
run_test "Automatic Physical-to-Lattice Scaling" "test_automatic_scaling"
run_test "Backward Compatibility" "test_backward_compatibility"
run_test "waLBerla Integration" "test_integration"

# Summary
echo "=========================================="
echo "Test Suite Summary"
echo "=========================================="
echo -e "Total Tests:  ${BLUE}$TOTAL_TESTS${NC}"
echo -e "Passed:       ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed:       ${RED}$FAILED_TESTS${NC}"
echo ""

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL TESTS PASSED! 🎉${NC}"
    echo ""
    echo "Enhanced Mesh Scaling System Features Verified:"
    echo "✓ Physical-to-lattice unit conversion"
    echo "✓ Multiple scaling modes (PRESERVE_ASPECT, AUTO_FIT, TARGET_SIZE)"
    echo "✓ Characteristic length scaling"
    echo "✓ Enhanced mesh preprocessing (normal computation, duplicate removal, validation)"
    echo "✓ Automatic mesh fitting to lattice regions"
    echo "✓ Backward compatibility with existing parameter files"
    echo "✓ Complete waLBerla simulation integration"
    echo ""
    echo -e "${GREEN}🚀 The system is ready for production use!${NC}"
    exit 0
else
    echo -e "${RED}❌ Some tests failed. Please check the output above.${NC}"
    exit 1
fi
