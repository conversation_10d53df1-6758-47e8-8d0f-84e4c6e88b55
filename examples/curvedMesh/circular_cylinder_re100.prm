//=================================================================================================
// waLBerla Parameter File: Circular Cylinder Re=100 Validation
// Enhanced Mesh Scaling System
//=================================================================================================

Parameters {
    // Simulation identification
    simulationName          "CircularCylinder_Re100_Validation";
    
    // Domain configuration
    domainSize              < 780, 520, 26 >;  // 30D x 20D x 1D lattice units
    periodic                < 0, 0, 1 >;       // Periodic in Z, walls in X,Y
    
    // Physical parameters (automatically calculated by enhanced scaling)
    reynoldsNumber          100.0;              // Target Reynolds number
    characteristicLength    26.0;               // Cylinder diameter in lattice units
    characteristicVelocity  0.0577;             // Lattice velocity (Ma = 0.1)
    kinematicViscosity      0.015;              // Lattice viscosity
    
    // Time stepping
    timesteps               200000;             // Sufficient for statistical convergence
    timestepsPerOutput      1000;               // Output frequency
    
    // LBM parameters
    omega                   1.8;                // Relaxation parameter (tau = 0.556)
    magicNumber             0.125;              // Standard LBM magic number
    
    // Convergence monitoring
    convergenceCheck        true;
    convergenceInterval     5000;
    convergenceThreshold    1e-8;
    
    // Force calculation
    calculateForces         true;
    forceOutputInterval     100;
    
    // Output configuration
    outputDirectory         "output_circular_cylinder_re100";
    writeVTK                true;
    vtkOutputInterval       5000;
    writeCheckpoint         true;
    checkpointInterval      50000;
}

DomainDecomposition {
    blocks                  < 4, 2, 1 >;       // MPI decomposition
    cellsPerBlock           < 195, 260, 26 >;  // Cells per block
}

Boundaries {
    // Inlet boundary (X = 0)
    Border {
        direction           W;
        walldistance        -1;
        
        Velocity {
            velocity        < 0.0577, 0, 0 >;  // Inlet velocity
        }
    }
    
    // Outlet boundary (X = 779)
    Border {
        direction           E;
        walldistance        -1;
        
        Outlet {
            normalDirection 0;
        }
    }
    
    // Top and bottom walls (Y = 0, Y = 519)
    Border {
        direction           N;
        walldistance        -1;
        
        NoSlip {}
    }
    
    Border {
        direction           S;
        walldistance        -1;
        
        NoSlip {}
    }
}

Geometry {
    // Enhanced mesh scaling configuration
    Triangle_mesh_scene {
        object {
            meshfile                "circular_cylinder.stl";
            position_L              < 260, 260, 13 >;      // Centered position (10D from inlet)
            objectdomainZ           26;                     // Cylinder diameter in lattice units
            outputForces            1;                      // Enable force calculation
            
            // Enhanced scaling options
            enableAutoScaling       1;                      // Enable automatic scaling
            characteristicLength    26.0;                   // Target diameter in lattice units
            reComputeTheNormals     1;                      // Recompute mesh normals
            checkNormConsistancy    1;                      // Check normal consistency
            centerMesh              0;                      // Don't center (use position_L)
            setPhysicalDimensions   1;                      // Track physical dimensions
            
            // Boundary condition
            flag                    NoSlip;
            
            // Floodfill configuration
            floodfill {
                point               < 390, 260, 13 >;       // Point in fluid region
                flag                Fluid;
            }
        }
    }
}

InitializationScheme {
    VelocityField {
        initialVelocity         < 0.0577, 0, 0 >;          // Uniform inlet velocity
    }
    
    DensityField {
        initialDensity          1.0;                        // Standard lattice density
    }
}

Output {
    // Force and moment output
    ForceEvaluation {
        filename                "forces_circular_cylinder.dat";
        writeFrequency          100;
        
        // Validation metrics calculation
        calculateDragCoefficient    true;
        calculateLiftCoefficient    true;
        calculateStrouhalNumber     true;
        
        // Reference values for coefficient calculation
        referenceVelocity       0.0577;                    // Lattice velocity
        referenceLength         26.0;                      // Cylinder diameter
        referenceArea           26.0;                      // Cylinder frontal area (2D)
        referenceDensity        1.0;                       // Lattice density
    }
    
    // Field output for visualization
    VTKOutput {
        filename                "field_circular_cylinder";
        writeFrequency          5000;
        
        // Output fields
        velocity                true;
        pressure                true;
        vorticity               true;
        
        // Slice output for 2D visualization
        sliceOutput             true;
        sliceDirection          2;                          // Z-direction
        slicePosition           13;                         // Middle slice
    }
    
    // Probe points for detailed analysis
    ProbeOutput {
        filename                "probes_circular_cylinder.dat";
        writeFrequency          100;
        
        // Wake measurement points
        probePoints {
            point1              < 286, 260, 13 >;          // 1D downstream
            point2              < 312, 260, 13 >;          // 2D downstream
            point3              < 364, 260, 13 >;          // 4D downstream
            point4              < 468, 260, 13 >;          // 8D downstream
        }
    }
    
    // Statistical analysis
    StatisticsOutput {
        filename                "statistics_circular_cylinder.dat";
        writeFrequency          1000;
        startTimestep           100000;                     // Start after initial transient
        
        // Validation metrics
        calculateMeanValues     true;
        calculateRMSValues      true;
        calculateReynoldsStresses true;
    }
}

Validation {
    // Literature benchmark values for comparison
    expectedDragCoefficient     1.33;
    dragCoefficientTolerance    0.05;
    
    expectedStrouhalNumber      0.164;
    strouhalNumberTolerance     0.005;
    
    expectedLiftRMS             0.25;
    liftRMSTolerance            0.02;
    
    // Validation reporting
    generateValidationReport    true;
    validationReportInterval    10000;
    validationReportFile        "validation_circular_cylinder.txt";
}
