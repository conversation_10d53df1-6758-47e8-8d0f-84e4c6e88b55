#=================================================================================================
# Real CMake Integration for Enhanced Mesh Scaling
# Integrates with actual waLBerla build system - no mock components
#=================================================================================================

cmake_minimum_required(VERSION 3.10)

# Find waLBerla package
find_package(walberla REQUIRED)

# Check for required waLBerla modules
if(NOT TARGET walberla::curvedboundary)
    message(WARNING "waLBerla curved boundary module not found. Please build waLBerla with WALBERLA_BUILD_WITH_CURVED_BOUNDARY=ON")
endif()

if(NOT TARGET walberla::turbulence)
    message(WARNING "waLBerla turbulence module not found. Please build waLBerla with WALBERLA_BUILD_WITH_TURBULENCE=ON")
endif()

# Real validation executables
add_executable(real_validation_runner 
    real_validation_runner.cpp
)

target_link_libraries(real_validation_runner
    walberla::core
    walberla::domain_decomposition
    walberla::field
    walberla::lbm
    walberla::curvedboundary
    walberla::turbulence
    walberla::timeloop
)

target_compile_features(real_validation_runner PRIVATE cxx_std_14)

# Real waLBerla integration test
add_executable(real_walberla_integration
    real_walberla_integration.cpp
)

target_link_libraries(real_walberla_integration
    walberla::core
    walberla::domain_decomposition
    walberla::field
    walberla::lbm
    walberla::curvedboundary
    walberla::turbulence
    walberla::timeloop
)

target_compile_features(real_walberla_integration PRIVATE cxx_std_14)

# Enhanced mesh scaling library (if building as separate component)
add_library(enhanced_mesh_scaling STATIC
    enhanced_mesh_scaling.cpp
)

target_link_libraries(enhanced_mesh_scaling
    walberla::core
    walberla::curvedboundary
)

target_include_directories(enhanced_mesh_scaling PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Real validation tests
enable_testing()

add_test(NAME real_cylinder_validation
    COMMAND real_validation_runner ${CMAKE_CURRENT_SOURCE_DIR}/real_validation_config.prm
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

add_test(NAME real_integration_test
    COMMAND real_walberla_integration
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

# Set test properties
set_tests_properties(real_cylinder_validation PROPERTIES
    TIMEOUT 300
    LABELS "validation;real"
)

set_tests_properties(real_integration_test PROPERTIES
    TIMEOUT 60
    LABELS "integration;real"
)

# Installation
install(TARGETS real_validation_runner real_walberla_integration
    RUNTIME DESTINATION bin
)

install(FILES 
    real_validation_config.prm
    literature_benchmarks.json
    DESTINATION share/walberla/validation
)

install(PROGRAMS
    real_force_analysis.py
    real_validation_workflow.sh
    DESTINATION bin
)

# Documentation
install(FILES
    REAL_FRAMEWORK_SUMMARY.md
    DESTINATION share/doc/walberla/enhanced_mesh_scaling
)

# Print configuration summary
message(STATUS "Real waLBerla Enhanced Mesh Scaling Configuration:")
message(STATUS "  waLBerla found: ${walberla_FOUND}")
message(STATUS "  Curved boundary: ${TARGET walberla::curvedboundary}")
message(STATUS "  Turbulence: ${TARGET walberla::turbulence}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")
