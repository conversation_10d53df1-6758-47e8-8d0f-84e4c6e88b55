cmake_minimum_required(VERSION 3.10)
project(CurvedMeshTests)

# Find waLBerla
find_package(walberla REQUIRED)

# Set C++ standard
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../../src)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../../extern)

# Test executables
add_executable(test_physical_units_converter 
    test_physical_units_converter.cpp
    ../../src/curvedboundary/mesh/PhysicalUnitsConverter.cpp
    ../../src/curvedboundary/mesh/TriangleMesh.cpp
)

add_executable(test_triangle_mesh_scaling 
    test_triangle_mesh_scaling.cpp
    ../../src/curvedboundary/mesh/TriangleMesh.cpp
    ../../src/curvedboundary/mesh/PhysicalUnitsConverter.cpp
)

add_executable(test_stl_reader_enhanced 
    test_stl_reader_enhanced.cpp
    ../../src/curvedboundary/readers/STLReader.cpp
    ../../src/curvedboundary/mesh/TriangleMesh.cpp
    ../../src/curvedboundary/mesh/PhysicalUnitsConverter.cpp
)

add_executable(test_automatic_scaling 
    test_automatic_scaling.cpp
    ../../src/curvedboundary/readers/STLReader.cpp
    ../../src/curvedboundary/mesh/TriangleMesh.cpp
    ../../src/curvedboundary/mesh/PhysicalUnitsConverter.cpp
)

add_executable(test_backward_compatibility 
    test_backward_compatibility.cpp
    ../../src/curvedboundary/readers/STLReader.cpp
    ../../src/curvedboundary/mesh/TriangleMesh.cpp
    ../../src/curvedboundary/mesh/PhysicalUnitsConverter.cpp
)

add_executable(test_integration 
    test_integration.cpp
    ../../src/curvedboundary/readers/STLReader.cpp
    ../../src/curvedboundary/mesh/TriangleMesh.cpp
    ../../src/curvedboundary/mesh/PhysicalUnitsConverter.cpp
)

# Link libraries
target_link_libraries(test_physical_units_converter ${walberla_LIBRARIES})
target_link_libraries(test_triangle_mesh_scaling ${walberla_LIBRARIES})
target_link_libraries(test_stl_reader_enhanced ${walberla_LIBRARIES})
target_link_libraries(test_automatic_scaling ${walberla_LIBRARIES})
target_link_libraries(test_backward_compatibility ${walberla_LIBRARIES})
target_link_libraries(test_integration ${walberla_LIBRARIES})

# Custom target to run all tests
add_custom_target(run_all_tests
    COMMAND echo "Running PhysicalUnitsConverter tests..."
    COMMAND ./test_physical_units_converter
    COMMAND echo "Running TriangleMesh scaling tests..."
    COMMAND ./test_triangle_mesh_scaling
    COMMAND echo "Running STLReader enhanced tests..."
    COMMAND ./test_stl_reader_enhanced
    COMMAND echo "Running automatic scaling tests..."
    COMMAND ./test_automatic_scaling
    COMMAND echo "Running backward compatibility tests..."
    COMMAND ./test_backward_compatibility
    COMMAND echo "Running integration tests..."
    COMMAND ./test_integration
    DEPENDS test_physical_units_converter test_triangle_mesh_scaling test_stl_reader_enhanced 
            test_automatic_scaling test_backward_compatibility test_integration
)
