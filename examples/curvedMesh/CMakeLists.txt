cmake_minimum_required(VERSION 3.10)

# Find waLBerla
find_package(walberla REQUIRED)

# Add validation runner executable
add_executable(real_validation_runner real_validation_runner.cpp)
target_link_libraries(real_validation_runner walberla::core walberla::curvedboundary walberla::turbulence)

# Add integration test executable  
add_executable(real_walberla_integration real_walberla_integration.cpp)
target_link_libraries(real_walberla_integration walberla::core walberla::curvedboundary walberla::turbulence)
