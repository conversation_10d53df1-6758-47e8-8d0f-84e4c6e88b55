//=================================================================================================
/*!
//  \file test_square_cylinder_re100.cpp
//  \brief Square cylinder flow validation at Re=100 with enhanced mesh scaling
*/
//=================================================================================================

#include <iostream>
#include <iomanip>
#include <cassert>
#include <cmath>
#include <vector>
#include <string>
#include <fstream>
#include <sstream>
#include <memory>

// Simplified types for testing
typedef double Real;
typedef unsigned int Uint;

template<typename T>
struct Vector3 {
    T data[3];
    Vector3() : data{0, 0, 0} {}
    Vector3(T x, T y, T z) : data{x, y, z} {}
    T& operator[](int i) { return data[i]; }
    const T& operator[](int i) const { return data[i]; }
    Vector3 operator*(T s) const { return Vector3(data[0]*s, data[1]*s, data[2]*s); }
    Vector3 operator+(const Vector3& v) const { return Vector3(data[0]+v[0], data[1]+v[1], data[2]+v[2]); }
    Vector3 operator-(const Vector3& v) const { return Vector3(data[0]-v[0], data[1]-v[1], data[2]-v[2]); }
};

// Square cylinder validation parameters for Re=100
struct SquareCylinderRe100 {
    // Physical parameters
    Real side_length;       // Square side length (m)
    Real length;           // Cylinder length (m)
    Real Uinf;             // Freestream velocity (m/s)
    Real nu;               // Kinematic viscosity (m²/s)
    Real Re;               // Reynolds number
    Real rho;              // Fluid density (kg/m³)
    
    // Expected results from literature (Sohankar et al. 1998, Okajima 1982)
    Real Cd_expected;      // Expected drag coefficient
    Real St_expected;      // Expected Strouhal number
    Real Cl_rms_expected;  // Expected RMS lift coefficient
    Real separation_angle; // Expected separation angle (degrees)
    
    // Domain parameters
    Real domain_length;    // Domain length (upstream + downstream)
    Real domain_width;     // Domain width
    Real domain_height;    // Domain height (for 3D)
    Real upstream_length;  // Distance from inlet to cylinder center
    Real downstream_length; // Distance from cylinder center to outlet
    
    // LBM parameters
    Real dx_target;        // Target physical grid spacing
    Real dt_target;        // Target physical time step
    Uint side_cells;       // Target square side in cells
    Real Ma_target;        // Target Mach number
    Real tau_target;       // Target relaxation time
    
    SquareCylinderRe100() {
        // Physical parameters for Re=100
        side_length = 0.04;            // 4 cm side length
        length = 0.04;                 // 4 cm length (for 3D)
        Re = 100.0;                    // Reynolds number
        Uinf = 0.535;                  // Freestream velocity (m/s)
        nu = Uinf * side_length / Re;  // Kinematic viscosity
        rho = 1.0;                     // Fluid density (kg/m³)
        
        // Expected results from literature
        // Square cylinder has different characteristics than circular
        Cd_expected = 1.48;            // Drag coefficient (higher than circular)
        St_expected = 0.148;           // Strouhal number (lower than circular)
        Cl_rms_expected = 0.12;        // RMS lift coefficient (lower than circular)
        separation_angle = 90.0;       // Sharp separation at corners
        
        // Domain sizing (similar to circular cylinder)
        upstream_length = 10.0 * side_length;    // 10D upstream
        downstream_length = 20.0 * side_length;  // 20D downstream
        domain_length = upstream_length + downstream_length;
        domain_width = 20.0 * side_length;       // 20D width
        domain_height = side_length;              // 1D height (for 3D)
        
        // LBM parameters for stable simulation
        side_cells = 26;                          // Square side in cells
        dx_target = side_length / side_cells;     // Physical grid spacing
        Ma_target = 0.1;                          // Target Mach number
        Real cs = 1.0/sqrt(3.0);                 // Lattice speed of sound
        Real u_lb = Ma_target * cs;               // Lattice velocity
        dt_target = dx_target * u_lb / Uinf;      // Physical time step
        tau_target = 3.0 * nu * dt_target / (dx_target * dx_target) + 0.5; // Relaxation time
    }
    
    void printParameters() const {
        std::cout << "Square Cylinder Re=100 Validation Parameters\n";
        std::cout << "============================================\n";
        std::cout << "Physical Parameters:\n";
        std::cout << "  Side length: " << side_length << " m\n";
        std::cout << "  Length: " << length << " m\n";
        std::cout << "  Reynolds number: " << Re << "\n";
        std::cout << "  Freestream velocity: " << Uinf << " m/s\n";
        std::cout << "  Kinematic viscosity: " << nu << " m²/s\n";
        std::cout << "  Fluid density: " << rho << " kg/m³\n\n";
        
        std::cout << "Expected Results (Literature):\n";
        std::cout << "  Drag coefficient: " << Cd_expected << "\n";
        std::cout << "  Strouhal number: " << St_expected << "\n";
        std::cout << "  RMS lift coefficient: " << Cl_rms_expected << "\n";
        std::cout << "  Separation angle: " << separation_angle << "°\n\n";
        
        std::cout << "Domain Parameters:\n";
        std::cout << "  Domain length: " << domain_length << " m (" << domain_length/side_length << "D)\n";
        std::cout << "  Domain width: " << domain_width << " m (" << domain_width/side_length << "D)\n";
        std::cout << "  Domain height: " << domain_height << " m (" << domain_height/side_length << "D)\n";
        std::cout << "  Upstream length: " << upstream_length << " m (" << upstream_length/side_length << "D)\n";
        std::cout << "  Downstream length: " << downstream_length << " m (" << downstream_length/side_length << "D)\n\n";
        
        std::cout << "LBM Parameters:\n";
        std::cout << "  Square side: " << side_cells << " cells\n";
        std::cout << "  Grid spacing: " << dx_target << " m\n";
        std::cout << "  Time step: " << dt_target << " s\n";
        std::cout << "  Target Mach number: " << Ma_target << "\n";
        std::cout << "  Target relaxation time: " << tau_target << "\n";
        std::cout << "  Lattice velocity: " << Ma_target/sqrt(3.0) << " lu/ts\n\n";
    }
    
    Vector3<Uint> getDomainSizeLattice() const {
        return Vector3<Uint>(
            static_cast<Uint>(domain_length / dx_target),
            static_cast<Uint>(domain_width / dx_target),
            static_cast<Uint>(domain_height / dx_target)
        );
    }
    
    Vector3<Real> getDomainSizePhysical() const {
        return Vector3<Real>(domain_length, domain_width, domain_height);
    }
    
    Vector3<Real> getCylinderPosition() const {
        return Vector3<Real>(
            upstream_length,           // X: upstream distance
            domain_width / 2.0,        // Y: centered
            domain_height / 2.0        // Z: centered
        );
    }
    
    Vector3<Uint> getCylinderPositionLattice() const {
        Vector3<Real> pos_phys = getCylinderPosition();
        return Vector3<Uint>(
            static_cast<Uint>(pos_phys[0] / dx_target),
            static_cast<Uint>(pos_phys[1] / dx_target),
            static_cast<Uint>(pos_phys[2] / dx_target)
        );
    }
};

// Simplified mesh and converter classes (reusing from previous implementation)
class TriangleMesh {
private:
    std::vector<Vector3<Real>> vertices_;
    Vector3<Real> bboxMin_, bboxMax_;
    Real totalArea_;
    std::string name_;
    bool isScaled_;
    std::string scalingInfo_;
    
public:
    TriangleMesh(const std::string& name = "Mesh") 
        : totalArea_(0.0), name_(name), isScaled_(false) {}
    
    void setBoundingBox(const Vector3<Real>& min, const Vector3<Real>& max) {
        bboxMin_ = min;
        bboxMax_ = max;
    }
    
    void setArea(Real area) { totalArea_ = area; }
    void setTriangleCount(size_t count) { vertices_.resize(count * 3); }
    
    Vector3<Real> getBoundingBoxMin() const { return bboxMin_; }
    Vector3<Real> getBoundingBoxMax() const { return bboxMax_; }
    Real getTotalArea() const { return totalArea_; }
    size_t getNumTriangles() const { return vertices_.size() / 3; }
    
    void applyPhysicalToLatticeScaling(const std::vector<Real>& scalingParams) {
        if(scalingParams.size() >= 4) {
            Real scaleFactor = scalingParams[0];
            Vector3<Real> offset(scalingParams[1], scalingParams[2], scalingParams[3]);
            
            bboxMin_ = bboxMin_ * scaleFactor + offset;
            bboxMax_ = bboxMax_ * scaleFactor + offset;
            totalArea_ *= scaleFactor * scaleFactor;
            
            isScaled_ = true;
            scalingInfo_ = "Physical-to-lattice scaling applied";
        }
    }
    
    bool isScaled() const { return isScaled_; }
    std::string getScalingInfo() const { return scalingInfo_; }
    
    void printInfo() const {
        std::cout << "Mesh: " << name_ << std::endl;
        std::cout << "  Triangles: " << getNumTriangles() << std::endl;
        std::cout << "  Bounding box: [" << bboxMin_[0] << ", " << bboxMin_[1] << ", " << bboxMin_[2] 
                  << "] to [" << bboxMax_[0] << ", " << bboxMax_[1] << ", " << bboxMax_[2] << "]" << std::endl;
        std::cout << "  Total area: " << totalArea_ << std::endl;
        std::cout << "  Scaled: " << (isScaled_ ? "Yes" : "No") << std::endl;
        if(isScaled_) {
            std::cout << "  Scaling info: " << scalingInfo_ << std::endl;
        }
    }
};

class PhysicalUnitsConverter {
private:
    Real dx_phys_, dt_phys_;
    Vector3<Real> domainSizePhys_;
    Real referenceLength_, referenceVelocity_;
    
public:
    PhysicalUnitsConverter(Real dx, Real dt, const Vector3<Real>& domainPhys) 
        : dx_phys_(dx), dt_phys_(dt), domainSizePhys_(domainPhys), 
          referenceLength_(1.0), referenceVelocity_(1.0) {}
    
    void setReferenceValues(Real Lref, Real Uref) {
        referenceLength_ = Lref;
        referenceVelocity_ = Uref;
    }
    
    Real getDx() const { return dx_phys_; }
    Real getDt() const { return dt_phys_; }
    
    std::vector<Real> scaleToCharacteristicLength(
        const Vector3<Real>& meshMin, const Vector3<Real>& meshMax,
        Real targetLength, Uint direction = 0) const {
        
        Real currentLength = meshMax[direction] - meshMin[direction];
        Real targetPhysLength = targetLength * dx_phys_;
        Real scaleFactor = targetPhysLength / currentLength;
        
        Vector3<Real> meshCenter((meshMin[0] + meshMax[0])/2,
                                (meshMin[1] + meshMax[1])/2,
                                (meshMin[2] + meshMax[2])/2);
        
        return {scaleFactor, -meshCenter[0] * scaleFactor, 
                -meshCenter[1] * scaleFactor, -meshCenter[2] * scaleFactor};
    }
    
    void printConversionSummary() const {
        std::cout << "Physical Units Converter Summary:\n";
        std::cout << "  dx_phys = " << dx_phys_ << " m\n";
        std::cout << "  dt_phys = " << dt_phys_ << " s\n";
        std::cout << "  Reference length = " << referenceLength_ << " m\n";
        std::cout << "  Reference velocity = " << referenceVelocity_ << " m/s\n";
        std::cout << "  Length scale: 1 lu = " << dx_phys_ << " m\n";
        std::cout << "  Time scale: 1 ts = " << dt_phys_ << " s\n";
        std::cout << "  Velocity scale: 1 lu/ts = " << dx_phys_/dt_phys_ << " m/s\n";
    }
};

bool loadSquareCylinderSTL(const std::string& filename, TriangleMesh& mesh) {
    // Simulate loading the square cylinder STL
    if(filename == "square_cylinder.stl") {
        // Set realistic square cylinder properties
        mesh.setBoundingBox(Vector3<Real>(-0.02, -0.02, 0.0), Vector3<Real>(0.02, 0.02, 0.04));
        mesh.setArea(4 * 0.04 * 0.04 + 2 * 0.04 * 0.04); // Square cylinder surface area
        mesh.setTriangleCount(12); // 12 triangles for a square cylinder (6 faces * 2 triangles each)
        return true;
    }
    return false;
}

void testSquareCylinderParameters() {
    std::cout << "\n=== Testing Square Cylinder Re=100 Parameters ===\n";

    SquareCylinderRe100 params;
    params.printParameters();

    // Verify Reynolds number calculation
    Real Re_calculated = params.Uinf * params.side_length / params.nu;
    assert(std::abs(Re_calculated - 100.0) < 1e-6);
    std::cout << "✓ Reynolds number verification: " << Re_calculated << "\n";

    // Verify domain sizing follows best practices
    assert(params.upstream_length / params.side_length >= 10.0);  // At least 10D upstream
    assert(params.downstream_length / params.side_length >= 20.0); // At least 20D downstream
    assert(params.domain_width / params.side_length >= 20.0);      // At least 20D width
    std::cout << "✓ Domain sizing follows best practices\n";

    // Verify LBM parameters are in stable range
    assert(params.Ma_target <= 0.1);           // Mach number < 0.1
    assert(params.tau_target > 0.5);           // Relaxation time > 0.5
    assert(params.side_cells >= 20);           // Sufficient resolution
    std::cout << "✓ LBM parameters are in stable range\n";

    // Verify square cylinder specific characteristics
    assert(params.Cd_expected > 1.4);          // Higher drag than circular cylinder
    assert(params.St_expected < 0.16);         // Lower Strouhal than circular cylinder
    assert(params.separation_angle == 90.0);   // Sharp separation at corners
    std::cout << "✓ Square cylinder specific characteristics verified\n";

    std::cout << "✓ Square cylinder parameters test passed\n";
}

void testMeshScalingForSquareCylinder() {
    std::cout << "\n=== Testing Mesh Scaling for Square Cylinder ===\n";

    SquareCylinderRe100 params;

    // Load square cylinder mesh
    TriangleMesh mesh("SquareCylinder");
    bool success = loadSquareCylinderSTL("square_cylinder.stl", mesh);
    assert(success);

    std::cout << "Original square cylinder mesh:\n";
    mesh.printInfo();

    // Setup units converter
    PhysicalUnitsConverter converter(params.dx_target, params.dt_target, params.getDomainSizePhysical());
    converter.setReferenceValues(params.side_length, params.Uinf);

    std::cout << "\nUnits converter setup:\n";
    converter.printConversionSummary();

    // Scale square side to target cells
    auto scalingParams = converter.scaleToCharacteristicLength(
        mesh.getBoundingBoxMin(), mesh.getBoundingBoxMax(),
        params.side_cells, 0); // Scale based on X-direction (side length)

    std::cout << "\nScaling parameters for " << params.side_cells << " cells side:\n";
    std::cout << "  Scale factor: " << scalingParams[0] << "\n";
    std::cout << "  Offsets: [" << scalingParams[1] << ", " << scalingParams[2] << ", " << scalingParams[3] << "]\n";

    // Apply scaling
    mesh.applyPhysicalToLatticeScaling(scalingParams);

    std::cout << "\nAfter scaling:\n";
    mesh.printInfo();

    // Verify scaling accuracy
    Vector3<Real> meshMin = mesh.getBoundingBoxMin();
    Vector3<Real> meshMax = mesh.getBoundingBoxMax();
    Real scaledSide = meshMax[0] - meshMin[0];
    Real expectedSide = params.side_cells * params.dx_target;

    std::cout << "\nScaling verification:\n";
    std::cout << "  Scaled side length: " << scaledSide << " m\n";
    std::cout << "  Expected side length: " << expectedSide << " m\n";
    std::cout << "  Side in lattice units: " << scaledSide / params.dx_target << " lu\n";
    std::cout << "  Target lattice units: " << params.side_cells << " lu\n";

    Real sideError = std::abs(scaledSide / params.dx_target - params.side_cells);
    assert(sideError < 0.1); // Within 0.1 lattice units

    std::cout << "✓ Mesh scaling test passed\n";
}

void testSquareVsCircularComparison() {
    std::cout << "\n=== Testing Square vs Circular Cylinder Comparison ===\n";

    SquareCylinderRe100 square_params;

    // Compare with typical circular cylinder values at Re=100
    Real circular_Cd = 1.33;
    Real circular_St = 0.164;
    Real circular_Cl_rms = 0.25;

    std::cout << "Comparison at Re=100:\n";
    std::cout << "                    Square    Circular   Difference\n";
    std::cout << "  Drag coefficient: " << std::setw(6) << square_params.Cd_expected
              << "    " << std::setw(6) << circular_Cd
              << "     " << std::setw(6) << (square_params.Cd_expected - circular_Cd) << "\n";
    std::cout << "  Strouhal number:  " << std::setw(6) << square_params.St_expected
              << "    " << std::setw(6) << circular_St
              << "     " << std::setw(6) << (square_params.St_expected - circular_St) << "\n";
    std::cout << "  RMS lift coeff:   " << std::setw(6) << square_params.Cl_rms_expected
              << "    " << std::setw(6) << circular_Cl_rms
              << "     " << std::setw(6) << (square_params.Cl_rms_expected - circular_Cl_rms) << "\n";

    // Verify expected differences
    assert(square_params.Cd_expected > circular_Cd);        // Square has higher drag
    assert(square_params.St_expected < circular_St);        // Square has lower Strouhal
    assert(square_params.Cl_rms_expected < circular_Cl_rms); // Square has lower lift fluctuations

    std::cout << "\n✓ Expected differences between square and circular cylinders verified\n";
    std::cout << "✓ Square vs circular comparison test passed\n";
}

void testCompleteSquareCylinderWorkflow() {
    std::cout << "\n=== Testing Complete Square Cylinder Workflow ===\n";

    SquareCylinderRe100 params;

    std::cout << "Setting up complete Re=100 square cylinder validation case...\n";

    // Step 1: Load and scale mesh
    TriangleMesh mesh("SquareCylinder_Re100");
    bool success = loadSquareCylinderSTL("square_cylinder.stl", mesh);
    assert(success);

    PhysicalUnitsConverter converter(params.dx_target, params.dt_target, params.getDomainSizePhysical());
    converter.setReferenceValues(params.side_length, params.Uinf);

    auto scalingParams = converter.scaleToCharacteristicLength(
        mesh.getBoundingBoxMin(), mesh.getBoundingBoxMax(), params.side_cells, 0);
    mesh.applyPhysicalToLatticeScaling(scalingParams);

    // Step 2: Position mesh in domain
    Vector3<Real> cylinderPos = params.getCylinderPosition();
    std::cout << "Square cylinder positioned at: [" << cylinderPos[0] << ", " << cylinderPos[1] << ", " << cylinderPos[2] << "] m\n";

    // Step 3: Verify simulation parameters
    std::cout << "\nSimulation ready with parameters:\n";
    std::cout << "  Reynolds number: " << params.Re << "\n";
    std::cout << "  Domain: " << params.getDomainSizeLattice()[0] << "x" << params.getDomainSizeLattice()[1] << "x" << params.getDomainSizeLattice()[2] << " cells\n";
    std::cout << "  Square side: " << params.side_cells << " cells\n";
    std::cout << "  Mach number: " << params.Ma_target << "\n";
    std::cout << "  Relaxation time: " << params.tau_target << "\n";

    // Step 4: Expected results
    std::cout << "\nExpected validation results:\n";
    std::cout << "  Drag coefficient: " << params.Cd_expected << " ± 0.05\n";
    std::cout << "  Strouhal number: " << params.St_expected << " ± 0.005\n";
    std::cout << "  RMS lift coefficient: " << params.Cl_rms_expected << " ± 0.02\n";
    std::cout << "  Separation angle: " << params.separation_angle << "° (sharp corners)\n";

    // Step 5: Validation considerations
    std::cout << "\nValidation considerations for square cylinder:\n";
    std::cout << "  - Sharp corners cause immediate flow separation\n";
    std::cout << "  - No boundary layer separation like circular cylinder\n";
    std::cout << "  - More predictable wake formation\n";
    std::cout << "  - Higher pressure drag due to bluff body shape\n";
    std::cout << "  - Lower frequency vortex shedding\n";

    // Verify all parameters are reasonable
    assert(mesh.isScaled());
    assert(params.Re == 100.0);
    assert(params.Ma_target <= 0.1);
    assert(params.tau_target > 0.5);

    std::cout << "✓ Complete square cylinder workflow test passed\n";
    std::cout << "🚀 Ready for Re=100 square cylinder validation!\n";
}

int main() {
    std::cout << "Testing Square Cylinder Re=100 Validation Case\n";
    std::cout << "===============================================\n";

    try {
        testSquareCylinderParameters();
        testMeshScalingForSquareCylinder();
        testSquareVsCircularComparison();
        testCompleteSquareCylinderWorkflow();

        std::cout << "\n🎉 All square cylinder Re=100 tests passed!\n";
        std::cout << "\nThe square cylinder validation case is properly configured:\n";
        std::cout << "✓ Re=100 flow parameters validated\n";
        std::cout << "✓ Mesh scaling system working correctly\n";
        std::cout << "✓ Domain setup follows best practices\n";
        std::cout << "✓ LBM parameters in stable range\n";
        std::cout << "✓ Expected results defined for validation\n";
        std::cout << "✓ Differences from circular cylinder understood\n";

        return 0;
    }
    catch(const std::exception& e) {
        std::cerr << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
}
