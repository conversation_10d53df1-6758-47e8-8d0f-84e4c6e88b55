//=================================================================================================
/*!
//  \file real_validation_runner.cpp
//  \brief Real waLBerla validation runner using actual ValidationSuite
//  
//  This demonstrates how to use waLBerla's actual ValidationSuite with the enhanced
//  mesh scaling system. No mock components - only real waLBerla interfaces.
*/
//=================================================================================================

#include "core/Environment.h"
#include "core/logging/Logging.h"
#include "core/config/Config.h"
#include "core/timing/TimingPool.h"

#include "domain_decomposition/StructuredBlockStorage.h"
#include "domain_decomposition/BlockDataID.h"

#include "field/AddToStorage.h"
#include "field/GhostLayerField.h"

#include "lbm/field/AddToStorage.h"
#include "lbm/field/PdfField.h"
#include "lbm/lattice_model/D3Q19.h"

// Real waLBerla curved boundary includes
#include "curvedboundary/CurvedBoundary.h"
#include "curvedboundary/mesh/TriangleMesh.h"
#include "curvedboundary/mesh/PhysicalUnitsConverter.h"
#include "curvedboundary/readers/STLReader.h"
#include "curvedboundary/validation/ValidationSuite.h"
#include "curvedboundary/forces/ForceOutput.h"

// Real waLBerla turbulence includes
#include "turbulence/TurbulenceModel.h"
#include "turbulence/SmagorinskyModel.h"

#include <fstream>
#include <iomanip>

using namespace walberla;

// Real waLBerla type definitions
using LatticeModel_T = lbm::D3Q19< lbm::collision_model::SRT >;
using PdfField_T = lbm::PdfField<LatticeModel_T>;
using VelField_T = GhostLayerField<Vector3<real_t>, 1>;
using DensField_T = GhostLayerField<real_t, 1>;
using FlagField_T = FlagField<uint8_t>;

//=================================================================================================
//  REAL VALIDATION RUNNER CLASS
//=================================================================================================

class RealValidationRunner {
private:
    std::shared_ptr<StructuredBlockStorage> blocks_;
    std::shared_ptr<curvedboundary::CurvedBoundary> curvedBC_;
    std::shared_ptr<curvedboundary::ValidationSuite> validationSuite_;
    std::shared_ptr<turbulence::TurbulenceModel> turbulenceModel_;
    
    // Field IDs
    BlockDataID pdfFieldId_;
    BlockDataID velFieldId_;
    BlockDataID densFieldId_;
    BlockDataID flagFieldId_;
    
    // Configuration
    Config::BlockHandle config_;
    
public:
    RealValidationRunner(const Config::BlockHandle& config) 
        : config_(config) {}
    
    bool initialize() {
        WALBERLA_LOG_INFO("Initializing real waLBerla validation runner");
        
        try {
            // Initialize domain
            if (!initializeDomain()) {
                WALBERLA_LOG_ERROR("Failed to initialize domain");
                return false;
            }
            
            // Initialize fields
            if (!initializeFields()) {
                WALBERLA_LOG_ERROR("Failed to initialize fields");
                return false;
            }
            
            // Initialize curved boundary
            if (!initializeCurvedBoundary()) {
                WALBERLA_LOG_ERROR("Failed to initialize curved boundary");
                return false;
            }
            
            // Initialize turbulence model
            initializeTurbulenceModel();
            
            // Initialize validation suite
            if (!initializeValidationSuite()) {
                WALBERLA_LOG_ERROR("Failed to initialize validation suite");
                return false;
            }
            
            WALBERLA_LOG_INFO("Real validation runner initialized successfully");
            return true;
            
        } catch (const std::exception& e) {
            WALBERLA_LOG_ERROR("Exception during initialization: " << e.what());
            return false;
        }
    }
    
private:
    bool initializeDomain() {
        auto domainConfig = config_.getBlock("Domain");
        
        Vector3<uint_t> cells = domainConfig.getParameter<Vector3<uint_t>>("cells");
        Vector3<real_t> size = domainConfig.getParameter<Vector3<real_t>>("size");
        Vector3<bool> periodic = domainConfig.getParameter<Vector3<bool>>("periodic", Vector3<bool>(false));
        
        blocks_ = blockforest::createUniformBlockGrid(
            cells[0], cells[1], cells[2],
            1, 1, 1,  // Single block for validation
            size[0], size[1], size[2],
            periodic[0], periodic[1], periodic[2]
        );
        
        WALBERLA_LOG_INFO("Domain: " << cells << " cells, size: " << size);
        return true;
    }
    
    bool initializeFields() {
        // Add real waLBerla fields
        pdfFieldId_ = lbm::addPdfFieldToStorage<LatticeModel_T>(
            blocks_, "pdf field", real_t(1), field::fzyx
        );
        
        velFieldId_ = field::addToStorage<VelField_T>(
            blocks_, "velocity field", Vector3<real_t>(0), field::fzyx, 1
        );
        
        densFieldId_ = field::addToStorage<DensField_T>(
            blocks_, "density field", real_t(1), field::fzyx, 1
        );
        
        flagFieldId_ = field::addFlagFieldToStorage<FlagField_T>(
            blocks_, "flag field"
        );
        
        return true;
    }
    
    bool initializeCurvedBoundary() {
        auto cbConfig = config_.getBlock("CurvedBoundary");
        
        // Create curved boundary handler
        curvedBC_ = std::make_shared<curvedboundary::CurvedBoundary>(
            blocks_, pdfFieldId_, velFieldId_, densFieldId_, flagFieldId_
        );
        
        // Load geometries with enhanced scaling
        auto geometryConfig = cbConfig.getBlock("Geometry");
        for (auto& geomBlock : geometryConfig) {
            if (!loadGeometry(geomBlock.second)) {
                return false;
            }
        }
        
        return true;
    }
    
    bool loadGeometry(const Config::BlockHandle& geomConfig) {
        std::string stlFile = geomConfig.getParameter<std::string>("stlFile");
        std::string name = geomConfig.getParameter<std::string>("name");
        
        // Check if STL file exists
        if (!std::ifstream(stlFile).good()) {
            WALBERLA_LOG_ERROR("STL file not found: " << stlFile);
            return false;
        }
        
        // Load STL using real waLBerla reader
        curvedboundary::STLReader reader;
        auto mesh = reader.readSTL(stlFile);
        
        if (!mesh) {
            WALBERLA_LOG_ERROR("Failed to load STL: " << stlFile);
            return false;
        }
        
        // Apply enhanced scaling if enabled
        bool useEnhancedScaling = geomConfig.getParameter<bool>("useEnhancedScaling", false);
        if (useEnhancedScaling) {
            real_t targetSize = geomConfig.getParameter<real_t>("targetCharacteristicLength");
            uint_t direction = geomConfig.getParameter<uint_t>("scalingDirection", 2);
            
            // Get physical parameters for scaling
            auto physicsConfig = config_.getBlock("Physics");
            real_t dx_phys = physicsConfig.getParameter<real_t>("gridSpacing", real_t(0.001));
            real_t dt_phys = physicsConfig.getParameter<real_t>("timeStep", real_t(0.001));
            
            // Create units converter
            curvedboundary::PhysicalUnitsConverter converter(dx_phys, dt_phys);
            
            // Apply scaling
            auto scalingParams = converter.getScalingParameters(
                mesh->getBoundingBox(), targetSize, direction
            );
            mesh->applyScaling(scalingParams);
            
            WALBERLA_LOG_INFO("Applied enhanced scaling to " << name << 
                            ": target = " << targetSize << " lattice units");
        }
        
        // Set position
        Vector3<real_t> position = geomConfig.getParameter<Vector3<real_t>>("position");
        mesh->setPosition(position);
        
        // Enable force calculation
        bool calculateForces = geomConfig.getParameter<bool>("calculateForces", false);
        if (calculateForces) {
            mesh->enableForceCalculation(true);
            
            // Set reference values
            real_t refArea = geomConfig.getParameter<real_t>("referenceArea", real_t(1.0));
            curvedboundary::ForceData& forceData = curvedBC_->getForceData(name);
            forceData.refArea = refArea;
        }
        
        // Add to curved boundary
        curvedBC_->addMesh(name, mesh);
        
        WALBERLA_LOG_INFO("Loaded geometry: " << name);
        return true;
    }
    
    void initializeTurbulenceModel() {
        if (!config_.isDefined("Turbulence")) {
            return;
        }
        
        auto turbConfig = config_.getBlock("Turbulence");
        std::string modelType = turbConfig.getParameter<std::string>("model", "none");
        
        if (modelType == "Smagorinsky") {
            real_t Cs = turbConfig.getParameter<real_t>("smagorinskyConstant", real_t(0.17));
            
            turbulenceModel_ = std::make_shared<turbulence::SmagorinskyModel>(
                blocks_, pdfFieldId_, velFieldId_, Cs
            );
            
            WALBERLA_LOG_INFO("Initialized Smagorinsky model with Cs = " << Cs);
        }
    }
    
    bool initializeValidationSuite() {
        if (!config_.isDefined("Validation") || 
            !config_.getBlock("Validation").getParameter<bool>("enabled", false)) {
            WALBERLA_LOG_INFO("Validation disabled");
            return true;
        }
        
        // Create validation suite
        validationSuite_ = std::make_shared<curvedboundary::ValidationSuite>();
        
        auto validationConfig = config_.getBlock("Validation");
        auto casesConfig = validationConfig.getBlock("cases");
        
        // Add validation cases based on configuration
        for (auto& caseBlock : casesConfig) {
            std::string caseName = caseBlock.first;
            auto caseConfig = caseBlock.second;
            
            if (!caseConfig.getParameter<bool>("enabled", true)) {
                continue;
            }
            
            if (caseName.find("cylinder") != std::string::npos) {
                addCylinderValidation(caseConfig);
            } else if (caseName.find("ahmed") != std::string::npos) {
                addAhmedBodyValidation(caseConfig);
            } else if (caseName.find("sphere") != std::string::npos) {
                addSphereValidation(caseConfig);
            }
        }
        
        WALBERLA_LOG_INFO("Validation suite initialized with " << 
                         validationSuite_->getNumCases() << " test cases");
        return true;
    }
    
    void addCylinderValidation(const Config::BlockHandle& caseConfig) {
        // Get physical parameters
        auto physicsConfig = config_.getBlock("Physics");
        real_t diameter = physicsConfig.getParameter<real_t>("referenceLength");
        real_t Re = physicsConfig.getParameter<real_t>("reynoldsNumber");
        
        // Create cylinder validation case
        auto cylinderValidation = std::make_shared<curvedboundary::CylinderFlowValidation>(
            diameter, Re
        );
        
        // Set expected results from configuration
        if (caseConfig.isDefined("expectedResults")) {
            auto expectedConfig = caseConfig.getBlock("expectedResults");
            
            // These would be set in the actual ValidationCase implementation
            // cylinderValidation->setExpectedDrag(expectedConfig.getParameter<real_t>("dragCoefficient"));
            // cylinderValidation->setExpectedStrouhal(expectedConfig.getParameter<real_t>("strouhalNumber"));
        }
        
        validationSuite_->addCase(cylinderValidation);
        WALBERLA_LOG_INFO("Added cylinder validation: Re = " << Re);
    }
    
    void addAhmedBodyValidation(const Config::BlockHandle& caseConfig) {
        real_t slantAngle = caseConfig.getParameter<real_t>("slantAngle", real_t(25.0));
        
        auto ahmedValidation = std::make_shared<curvedboundary::AhmedBodyValidation>(slantAngle);
        validationSuite_->addCase(ahmedValidation);
        
        WALBERLA_LOG_INFO("Added Ahmed body validation: slant = " << slantAngle << "°");
    }
    
    void addSphereValidation(const Config::BlockHandle& caseConfig) {
        auto physicsConfig = config_.getBlock("Physics");
        real_t diameter = physicsConfig.getParameter<real_t>("referenceLength");
        real_t Re = physicsConfig.getParameter<real_t>("reynoldsNumber");
        
        auto sphereValidation = std::make_shared<curvedboundary::SphereDragValidation>(
            diameter, Re
        );
        validationSuite_->addCase(sphereValidation);
        
        WALBERLA_LOG_INFO("Added sphere validation: Re = " << Re);
    }
    
public:
    bool runValidation() {
        if (!validationSuite_) {
            WALBERLA_LOG_INFO("No validation suite configured");
            return true;
        }
        
        WALBERLA_LOG_INFO("Running real waLBerla validation suite");
        
        try {
            // Create domain wrapper for validation
            Domain domain(blocks_, pdfFieldId_, velFieldId_, densFieldId_, flagFieldId_);
            
            // Run all validation cases
            validationSuite_->runAll(domain, *curvedBC_, turbulenceModel_);
            
            // Generate validation report
            auto validationConfig = config_.getBlock("Validation");
            std::string reportFile = validationConfig.getParameter<std::string>(
                "reportFile", "validation_report.txt"
            );
            
            validationSuite_->generateReport(reportFile);
            
            // Check results
            bool allPassed = validationSuite_->allPassed();
            auto summary = validationSuite_->getSummary();
            
            WALBERLA_LOG_INFO("Validation Results:");
            WALBERLA_LOG_INFO("  Total tests: " << summary.totalTests);
            WALBERLA_LOG_INFO("  Passed: " << summary.passed);
            WALBERLA_LOG_INFO("  Failed: " << summary.failed);
            WALBERLA_LOG_INFO("  Average error: " << summary.averageError);
            WALBERLA_LOG_INFO("  Max error: " << summary.maxError);
            
            if (allPassed) {
                WALBERLA_LOG_INFO("✅ All validation tests passed!");
            } else {
                WALBERLA_LOG_WARNING("⚠️ Some validation tests failed");
            }
            
            // Save detailed results
            saveValidationResults(summary);
            
            return allPassed;
            
        } catch (const std::exception& e) {
            WALBERLA_LOG_ERROR("Exception during validation: " << e.what());
            return false;
        }
    }
    
private:
    void saveValidationResults(const curvedboundary::ValidationSuite::SummaryStats& summary) {
        std::ofstream resultsFile("validation_results.json");
        
        resultsFile << "{\n";
        resultsFile << "  \"validation_summary\": {\n";
        resultsFile << "    \"total_tests\": " << summary.totalTests << ",\n";
        resultsFile << "    \"passed\": " << summary.passed << ",\n";
        resultsFile << "    \"failed\": " << summary.failed << ",\n";
        resultsFile << "    \"success_rate\": " << (real_t(summary.passed) / summary.totalTests * 100) << ",\n";
        resultsFile << "    \"average_error\": " << summary.averageError << ",\n";
        resultsFile << "    \"max_error\": " << summary.maxError << "\n";
        resultsFile << "  },\n";
        resultsFile << "  \"timestamp\": \"" << std::time(nullptr) << "\",\n";
        resultsFile << "  \"framework\": \"waLBerla Enhanced Mesh Scaling\"\n";
        resultsFile << "}\n";
        
        resultsFile.close();
        WALBERLA_LOG_INFO("Validation results saved to validation_results.json");
    }
};

//=================================================================================================
//  MAIN FUNCTION
//=================================================================================================

int main(int argc, char** argv) {
    Environment env(argc, argv);
    
    WALBERLA_LOG_INFO("Starting real waLBerla validation runner");
    
    // Load configuration
    auto config = env.config();
    if (!config) {
        WALBERLA_LOG_ERROR("No configuration provided");
        return 1;
    }
    
    try {
        // Create and initialize validation runner
        RealValidationRunner runner(config->getGlobalBlock());
        
        if (!runner.initialize()) {
            WALBERLA_LOG_ERROR("Failed to initialize validation runner");
            return 1;
        }
        
        // Run validation
        bool success = runner.runValidation();
        
        if (success) {
            WALBERLA_LOG_INFO("✅ Real waLBerla validation completed successfully");
            return 0;
        } else {
            WALBERLA_LOG_ERROR("❌ Validation failed");
            return 1;
        }
        
    } catch (const std::exception& e) {
        WALBERLA_LOG_ERROR("Exception: " << e.what());
        return 1;
    }
}
