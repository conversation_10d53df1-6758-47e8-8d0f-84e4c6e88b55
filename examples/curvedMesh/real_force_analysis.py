#!/usr/bin/env python3
"""
Real waLBerla Force Analysis Tool
Processes actual waLBerla force output files and compares against literature benchmarks
No mock data - only real waLBerla output formats
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import json
import argparse
import sys
from pathlib import Path
from scipy import signal
from scipy.fft import fft, fftfreq

class RealWalberlaForceAnalyzer:
    """Analyzes real waLBerla force output files"""
    
    def __init__(self, force_file, benchmarks_file="literature_benchmarks.json"):
        """
        Initialize with real waLBerla force file
        
        Args:
            force_file: Path to waLBerla force output file
            benchmarks_file: Path to literature benchmarks JSON
        """
        self.force_file = Path(force_file)
        self.benchmarks_file = Path(benchmarks_file)
        self.data = None
        self.benchmarks = None
        self.results = {}
        
        # Load literature benchmarks
        self.load_benchmarks()
        
    def load_benchmarks(self):
        """Load real literature benchmarks from JSON file"""
        try:
            with open(self.benchmarks_file, 'r') as f:
                self.benchmarks = json.load(f)
            print(f"Loaded literature benchmarks from {self.benchmarks_file}")
        except FileNotFoundError:
            print(f"Warning: Benchmarks file {self.benchmarks_file} not found")
            self.benchmarks = {}
    
    def load_walberla_forces(self):
        """Load real waLBerla force output file"""
        try:
            # waLBerla force files typically have format:
            # timestep time Fx Fy Fz Mx My Mz
            # or
            # timestep Fx Fy Fz (simplified format)
            
            # Try to detect format automatically
            with open(self.force_file, 'r') as f:
                first_line = f.readline().strip()
                
            # Skip comment lines
            while first_line.startswith('#') or first_line.startswith('%'):
                with open(self.force_file, 'r') as f:
                    lines = f.readlines()
                    for line in lines:
                        if not line.strip().startswith('#') and not line.strip().startswith('%'):
                            first_line = line.strip()
                            break
            
            # Count columns to determine format
            num_cols = len(first_line.split())
            
            if num_cols >= 8:
                # Full format: timestep time Fx Fy Fz Mx My Mz
                column_names = ['timestep', 'time', 'Fx', 'Fy', 'Fz', 'Mx', 'My', 'Mz']
            elif num_cols >= 4:
                # Simplified format: timestep Fx Fy Fz
                column_names = ['timestep', 'Fx', 'Fy', 'Fz']
            else:
                raise ValueError(f"Unexpected number of columns: {num_cols}")
            
            # Load data
            self.data = pd.read_csv(self.force_file, sep=r'\s+', comment='#', 
                                  names=column_names[:num_cols])
            
            # Add time column if not present (assume unit time step)
            if 'time' not in self.data.columns:
                self.data['time'] = self.data['timestep']
            
            print(f"Loaded {len(self.data)} force samples from {self.force_file}")
            print(f"Time range: {self.data['time'].min():.1f} to {self.data['time'].max():.1f}")
            
            return True
            
        except Exception as e:
            print(f"Error loading waLBerla force file: {e}")
            return False
    
    def calculate_coefficients(self, rho=1.0, U=1.0, A=1.0, start_fraction=0.5):
        """
        Calculate force coefficients from real waLBerla data
        
        Args:
            rho: Fluid density
            U: Reference velocity
            A: Reference area
            start_fraction: Fraction of data to use for averaging (from end)
        """
        if self.data is None:
            print("Error: No force data loaded")
            return
        
        # Use last portion of data for statistical analysis
        start_idx = int(len(self.data) * start_fraction)
        force_data = self.data.iloc[start_idx:].copy()
        
        # Calculate dynamic pressure
        q = 0.5 * rho * U**2
        
        # Calculate force coefficients
        force_data['Cd'] = force_data['Fx'] / (q * A)
        force_data['Cl'] = force_data['Fy'] / (q * A)
        
        # Statistical analysis
        self.results = {
            'Cd_mean': float(force_data['Cd'].mean()),
            'Cd_std': float(force_data['Cd'].std()),
            'Cl_mean': float(force_data['Cl'].mean()),
            'Cl_std': float(force_data['Cl'].std()),
            'Cl_rms': float(np.sqrt(force_data['Cl'].var())),
            
            # Raw forces
            'Fx_mean': float(force_data['Fx'].mean()),
            'Fy_mean': float(force_data['Fy'].mean()),
            'Fx_std': float(force_data['Fx'].std()),
            'Fy_std': float(force_data['Fy'].std()),
            
            # Analysis parameters
            'samples_used': len(force_data),
            'time_range': [float(force_data['time'].min()), float(force_data['time'].max())],
            'reference_values': {'rho': rho, 'U': U, 'A': A, 'q': q}
        }
        
        print(f"Calculated coefficients from {len(force_data)} samples")
        print(f"Drag coefficient: {self.results['Cd_mean']:.4f} ± {self.results['Cd_std']:.4f}")
        print(f"Lift coefficient: {self.results['Cl_mean']:.4f} ± {self.results['Cl_std']:.4f}")
        
        return self.results
    
    def calculate_strouhal_number(self, U=1.0, L=1.0, force_component='Fy'):
        """
        Calculate Strouhal number from force oscillations
        
        Args:
            U: Reference velocity
            L: Reference length
            force_component: Force component to analyze ('Fx', 'Fy', 'Fz')
        """
        if self.data is None:
            print("Error: No force data loaded")
            return None
        
        # Use last 50% of data for frequency analysis
        start_idx = len(self.data) // 2
        force_signal = self.data[force_component].iloc[start_idx:].values
        time_signal = self.data['time'].iloc[start_idx:].values
        
        # Calculate sampling frequency
        dt = np.mean(np.diff(time_signal))
        fs = 1.0 / dt
        
        # Remove mean and apply window
        force_signal = force_signal - np.mean(force_signal)
        window = signal.windows.hann(len(force_signal))
        force_signal = force_signal * window
        
        # Calculate power spectral density
        frequencies, psd = signal.welch(force_signal, fs, nperseg=len(force_signal)//4)
        
        # Find dominant frequency (skip DC component)
        peak_idx = np.argmax(psd[1:]) + 1
        dominant_frequency = frequencies[peak_idx]
        
        # Calculate Strouhal number
        strouhal_number = dominant_frequency * L / U
        
        self.results['St'] = float(strouhal_number)
        self.results['dominant_frequency'] = float(dominant_frequency)
        self.results['peak_psd'] = float(psd[peak_idx])
        
        print(f"Strouhal number: {strouhal_number:.4f}")
        print(f"Dominant frequency: {dominant_frequency:.4f} Hz")
        
        return strouhal_number
    
    def compare_with_literature(self, case_type, reynolds_number):
        """
        Compare results with literature benchmarks
        
        Args:
            case_type: Type of case ('circular_cylinder', 'square_cylinder', etc.)
            reynolds_number: Reynolds number for comparison
        """
        if not self.benchmarks or case_type not in self.benchmarks:
            print(f"No literature benchmarks available for {case_type}")
            return
        
        # Find closest Reynolds number case
        case_data = self.benchmarks[case_type]
        best_match = None
        min_re_diff = float('inf')
        
        for re_case, data in case_data.items():
            if 'conditions' in data and 'reynolds_number' in data['conditions']:
                re_lit = data['conditions']['reynolds_number']
                re_diff = abs(re_lit - reynolds_number)
                if re_diff < min_re_diff:
                    min_re_diff = re_diff
                    best_match = (re_case, data)
        
        if best_match is None:
            print(f"No matching Reynolds number found for {case_type}")
            return
        
        re_case, lit_data = best_match
        print(f"\nComparing with literature: {lit_data['source']} (Re={lit_data['conditions']['reynolds_number']})")
        print(f"Citation: {lit_data['citation']}")
        
        # Compare available metrics
        comparison = {}
        
        if 'results' in lit_data:
            lit_results = lit_data['results']
            
            # Compare drag coefficient
            if 'drag_coefficient' in lit_results and 'Cd_mean' in self.results:
                lit_cd = lit_results['drag_coefficient']['value']
                sim_cd = self.results['Cd_mean']
                error = abs(sim_cd - lit_cd) / lit_cd * 100
                
                comparison['drag_coefficient'] = {
                    'literature': lit_cd,
                    'simulation': sim_cd,
                    'error_percent': error,
                    'tolerance': lit_results['drag_coefficient'].get('uncertainty', 0.05) * 100
                }
                
                print(f"Drag coefficient: {sim_cd:.4f} vs {lit_cd:.4f} (error: {error:.1f}%)")
            
            # Compare Strouhal number
            if 'strouhal_number' in lit_results and 'St' in self.results:
                lit_st = lit_results['strouhal_number']['value']
                sim_st = self.results['St']
                error = abs(sim_st - lit_st) / lit_st * 100
                
                comparison['strouhal_number'] = {
                    'literature': lit_st,
                    'simulation': sim_st,
                    'error_percent': error,
                    'tolerance': lit_results['strouhal_number'].get('uncertainty', 0.005) * 100
                }
                
                print(f"Strouhal number: {sim_st:.4f} vs {lit_st:.4f} (error: {error:.1f}%)")
            
            # Compare lift RMS
            if 'lift_coefficient_rms' in lit_results and 'Cl_rms' in self.results:
                lit_cl_rms = lit_results['lift_coefficient_rms']['value']
                sim_cl_rms = self.results['Cl_rms']
                error = abs(sim_cl_rms - lit_cl_rms) / lit_cl_rms * 100
                
                comparison['lift_rms'] = {
                    'literature': lit_cl_rms,
                    'simulation': sim_cl_rms,
                    'error_percent': error,
                    'tolerance': lit_results['lift_coefficient_rms'].get('uncertainty', 0.02) * 100
                }
                
                print(f"Lift RMS: {sim_cl_rms:.4f} vs {lit_cl_rms:.4f} (error: {error:.1f}%)")
        
        self.results['literature_comparison'] = comparison
        return comparison
    
    def generate_plots(self, output_dir="."):
        """Generate analysis plots"""
        if self.data is None:
            print("Error: No data to plot")
            return
        
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # Time series plot
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('waLBerla Force Analysis')
        
        # Force time series
        axes[0,0].plot(self.data['time'], self.data['Fx'], label='Fx')
        axes[0,0].plot(self.data['time'], self.data['Fy'], label='Fy')
        axes[0,0].set_xlabel('Time')
        axes[0,0].set_ylabel('Force')
        axes[0,0].legend()
        axes[0,0].set_title('Force Time Series')
        axes[0,0].grid(True, alpha=0.3)
        
        # Force coefficients (if calculated)
        if 'Cd_mean' in self.results:
            # Calculate coefficients for plotting
            q = self.results['reference_values']['q']
            A = self.results['reference_values']['A']
            
            Cd_series = self.data['Fx'] / (q * A)
            Cl_series = self.data['Fy'] / (q * A)
            
            axes[0,1].plot(self.data['time'], Cd_series, label='Cd')
            axes[0,1].plot(self.data['time'], Cl_series, label='Cl')
            axes[0,1].axhline(y=self.results['Cd_mean'], color='red', linestyle='--', alpha=0.7, label='Cd mean')
            axes[0,1].set_xlabel('Time')
            axes[0,1].set_ylabel('Force Coefficient')
            axes[0,1].legend()
            axes[0,1].set_title('Force Coefficients')
            axes[0,1].grid(True, alpha=0.3)
        
        # Power spectral density
        if len(self.data) > 100:
            start_idx = len(self.data) // 2
            force_signal = self.data['Fy'].iloc[start_idx:].values
            time_signal = self.data['time'].iloc[start_idx:].values
            
            dt = np.mean(np.diff(time_signal))
            fs = 1.0 / dt
            
            frequencies, psd = signal.welch(force_signal - np.mean(force_signal), fs)
            
            axes[1,0].loglog(frequencies[1:], psd[1:])  # Skip DC component
            axes[1,0].set_xlabel('Frequency')
            axes[1,0].set_ylabel('PSD')
            axes[1,0].set_title('Power Spectral Density (Fy)')
            axes[1,0].grid(True, alpha=0.3)
            
            if 'dominant_frequency' in self.results:
                axes[1,0].axvline(x=self.results['dominant_frequency'], color='red', 
                                linestyle='--', label=f"f = {self.results['dominant_frequency']:.4f}")
                axes[1,0].legend()
        
        # Statistics
        if 'Cd_mean' in self.results:
            stats_text = f"Cd = {self.results['Cd_mean']:.4f} ± {self.results['Cd_std']:.4f}\n"
            stats_text += f"Cl = {self.results['Cl_mean']:.4f} ± {self.results['Cl_std']:.4f}\n"
            if 'St' in self.results:
                stats_text += f"St = {self.results['St']:.4f}"
            
            axes[1,1].text(0.1, 0.5, stats_text, transform=axes[1,1].transAxes, 
                          fontsize=12, verticalalignment='center',
                          bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
            axes[1,1].set_title('Statistics')
            axes[1,1].axis('off')
        
        plt.tight_layout()
        plt.savefig(output_dir / "force_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Analysis plots saved to {output_dir / 'force_analysis.png'}")
    
    def save_results(self, output_file):
        """Save analysis results to JSON file"""
        output_file = Path(output_file)
        
        # Prepare results for JSON serialization
        results_json = {
            'analysis_info': {
                'force_file': str(self.force_file),
                'benchmarks_file': str(self.benchmarks_file),
                'analysis_date': pd.Timestamp.now().isoformat()
            },
            'results': self.results
        }
        
        with open(output_file, 'w') as f:
            json.dump(results_json, f, indent=2)
        
        print(f"Results saved to {output_file}")

def main():
    parser = argparse.ArgumentParser(description='Analyze real waLBerla force output files')
    parser.add_argument('force_file', help='waLBerla force output file')
    parser.add_argument('--benchmarks', default='literature_benchmarks.json', 
                       help='Literature benchmarks JSON file')
    parser.add_argument('--case-type', choices=['circular_cylinder', 'square_cylinder', 'ahmed_body', 'sphere'],
                       help='Type of validation case')
    parser.add_argument('--reynolds', type=float, help='Reynolds number for literature comparison')
    parser.add_argument('--rho', type=float, default=1.0, help='Fluid density')
    parser.add_argument('--U', type=float, default=1.0, help='Reference velocity')
    parser.add_argument('--A', type=float, default=1.0, help='Reference area')
    parser.add_argument('--L', type=float, default=1.0, help='Reference length')
    parser.add_argument('--output', help='Output file for results (JSON)')
    parser.add_argument('--plots', help='Directory for output plots')
    
    args = parser.parse_args()
    
    # Initialize analyzer
    analyzer = RealWalberlaForceAnalyzer(args.force_file, args.benchmarks)
    
    # Load force data
    if not analyzer.load_walberla_forces():
        sys.exit(1)
    
    # Calculate coefficients
    analyzer.calculate_coefficients(args.rho, args.U, args.A)
    
    # Calculate Strouhal number
    analyzer.calculate_strouhal_number(args.U, args.L)
    
    # Compare with literature if specified
    if args.case_type and args.reynolds:
        analyzer.compare_with_literature(args.case_type, args.reynolds)
    
    # Generate plots
    if args.plots:
        analyzer.generate_plots(args.plots)
    
    # Save results
    if args.output:
        analyzer.save_results(args.output)
    
    # Print summary
    print("\n" + "="*50)
    print("REAL WALBERLA FORCE ANALYSIS SUMMARY")
    print("="*50)
    for key, value in analyzer.results.items():
        if isinstance(value, (int, float)):
            print(f"{key}: {value:.6f}")
        elif isinstance(value, dict) and key == 'literature_comparison':
            print(f"\nLiterature Comparison:")
            for metric, comp in value.items():
                error = comp['error_percent']
                tolerance = comp['tolerance']
                status = "✓" if error < tolerance else "✗"
                print(f"  {metric}: {error:.1f}% error {status}")

if __name__ == "__main__":
    main()
