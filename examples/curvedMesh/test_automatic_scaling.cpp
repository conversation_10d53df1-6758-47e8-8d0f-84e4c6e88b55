//=================================================================================================
/*!
//  \file test_automatic_scaling.cpp
//  \brief Test complete automatic physical-to-lattice scaling pipeline
*/
//=================================================================================================

#include <iostream>
#include <iomanip>
#include <cassert>
#include <cmath>
#include <vector>
#include <string>
#include <fstream>
#include <sstream>
#include <memory>
#include <algorithm>

// Simplified types for testing
typedef double Real;
typedef unsigned int Uint;

template<typename T>
struct Vector3 {
    T data[3];
    Vector3() : data{0, 0, 0} {}
    Vector3(T x, T y, T z) : data{x, y, z} {}
    T& operator[](int i) { return data[i]; }
    const T& operator[](int i) const { return data[i]; }
    Vector3 operator*(T s) const { return Vector3(data[0]*s, data[1]*s, data[2]*s); }
    Vector3 operator+(const Vector3& v) const { return Vector3(data[0]+v[0], data[1]+v[1], data[2]+v[2]); }
    Vector3 operator-(const Vector3& v) const { return Vector3(data[0]-v[0], data[1]-v[1], data[2]-v[2]); }
};

// Simplified Triangle structure
struct Triangle {
    Vector3<Real> vertices[3];
    Vector3<Real> normal;
    Real area;
    
    Triangle() : area(0.0) {}
    Triangle(const Vector3<Real>& v0, const Vector3<Real>& v1, const Vector3<Real>& v2) {
        vertices[0] = v0;
        vertices[1] = v1;
        vertices[2] = v2;
        computeProperties();
    }
    
    void computeProperties() {
        Vector3<Real> edge1 = vertices[1] - vertices[0];
        Vector3<Real> edge2 = vertices[2] - vertices[0];
        
        // Cross product for normal
        normal[0] = edge1[1] * edge2[2] - edge1[2] * edge2[1];
        normal[1] = edge1[2] * edge2[0] - edge1[0] * edge2[2];
        normal[2] = edge1[0] * edge2[1] - edge1[1] * edge2[0];
        
        // Area = 0.5 * |cross product|
        Real length = std::sqrt(normal[0]*normal[0] + normal[1]*normal[1] + normal[2]*normal[2]);
        area = 0.5 * length;
        
        // Normalize normal
        if(length > 1e-12) {
            normal[0] /= length;
            normal[1] /= length;
            normal[2] /= length;
        }
    }
};

// Simplified TriangleMesh
class TriangleMesh {
private:
    std::vector<Vector3<Real>> vertices_;
    std::vector<Triangle> triangles_;
    Vector3<Real> bboxMin_, bboxMax_;
    Vector3<Real> physicalBBoxMin_, physicalBBoxMax_;
    bool hasPhysicalDimensions_;
    bool isScaled_;
    std::string scalingInfo_;
    Real totalArea_;
    std::string name_;
    
public:
    TriangleMesh(const std::string& name = "TestMesh") 
        : hasPhysicalDimensions_(false), isScaled_(false), totalArea_(0.0), name_(name) {}
    
    void addTriangle(const Vector3<Real>& v0, const Vector3<Real>& v1, const Vector3<Real>& v2) {
        triangles_.emplace_back(v0, v1, v2);
        vertices_.push_back(v0);
        vertices_.push_back(v1);
        vertices_.push_back(v2);
    }
    
    void finalize() {
        computeBoundingBox();
        computeTotalArea();
    }
    
    void computeBoundingBox() {
        if(vertices_.empty()) return;
        
        bboxMin_ = bboxMax_ = vertices_[0];
        for(const auto& v : vertices_) {
            for(int i = 0; i < 3; ++i) {
                bboxMin_[i] = std::min(bboxMin_[i], v[i]);
                bboxMax_[i] = std::max(bboxMax_[i], v[i]);
            }
        }
    }
    
    void computeTotalArea() {
        totalArea_ = 0.0;
        for(const auto& tri : triangles_) {
            totalArea_ += tri.area;
        }
    }
    
    Vector3<Real> getBoundingBoxMin() const { return bboxMin_; }
    Vector3<Real> getBoundingBoxMax() const { return bboxMax_; }
    Real getTotalArea() const { return totalArea_; }
    size_t getNumTriangles() const { return triangles_.size(); }
    
    void setPhysicalDimensions(const Vector3<Real>& physMin, const Vector3<Real>& physMax) {
        physicalBBoxMin_ = physMin;
        physicalBBoxMax_ = physMax;
        hasPhysicalDimensions_ = true;
    }
    
    bool getPhysicalDimensions(Vector3<Real>& physMin, Vector3<Real>& physMax) const {
        if(hasPhysicalDimensions_) {
            physMin = physicalBBoxMin_;
            physMax = physicalBBoxMax_;
            return true;
        }
        return false;
    }
    
    void applyPhysicalToLatticeScaling(const std::vector<Real>& scalingParams) {
        if(scalingParams.size() == 4) {
            // Uniform scaling: [scale_factor, offset_x, offset_y, offset_z]
            scaleUniform(scalingParams[0], Vector3<Real>(scalingParams[1], scalingParams[2], scalingParams[3]));
        } else if(scalingParams.size() >= 6) {
            // Non-uniform scaling: [scale_x, scale_y, scale_z, offset_x, offset_y, offset_z]
            Vector3<Real> scaleFactors(scalingParams[0], scalingParams[1], scalingParams[2]);
            Vector3<Real> offset(scalingParams[3], scalingParams[4], scalingParams[5]);
            scaleNonUniform(scaleFactors, offset);
        }
        
        isScaled_ = true;
        scalingInfo_ = "Physical-to-lattice scaling applied";
    }
    
    void scaleUniform(Real scaleFactor, const Vector3<Real>& offset) {
        for(auto& vertex : vertices_) {
            vertex = vertex * scaleFactor + offset;
        }
        for(auto& triangle : triangles_) {
            for(int i = 0; i < 3; ++i) {
                triangle.vertices[i] = triangle.vertices[i] * scaleFactor + offset;
            }
            triangle.computeProperties();
        }
        computeBoundingBox();
        totalArea_ *= scaleFactor * scaleFactor;
        isScaled_ = true;
        scalingInfo_ = "Uniform scaling: " + std::to_string(scaleFactor);
    }
    
    void scaleNonUniform(const Vector3<Real>& scaleFactors, const Vector3<Real>& offset) {
        for(auto& vertex : vertices_) {
            vertex[0] = vertex[0] * scaleFactors[0] + offset[0];
            vertex[1] = vertex[1] * scaleFactors[1] + offset[1];
            vertex[2] = vertex[2] * scaleFactors[2] + offset[2];
        }
        for(auto& triangle : triangles_) {
            for(int i = 0; i < 3; ++i) {
                triangle.vertices[i][0] = triangle.vertices[i][0] * scaleFactors[0] + offset[0];
                triangle.vertices[i][1] = triangle.vertices[i][1] * scaleFactors[1] + offset[1];
                triangle.vertices[i][2] = triangle.vertices[i][2] * scaleFactors[2] + offset[2];
            }
            triangle.computeProperties();
        }
        computeBoundingBox();
        Real areaScaleFactor = std::sqrt(scaleFactors[0] * scaleFactors[1] * scaleFactors[0] * scaleFactors[2] * scaleFactors[1] * scaleFactors[2]) / 3.0;
        totalArea_ *= areaScaleFactor;
        isScaled_ = true;
        scalingInfo_ = "Non-uniform scaling: [" + std::to_string(scaleFactors[0]) + ", " + 
                       std::to_string(scaleFactors[1]) + ", " + std::to_string(scaleFactors[2]) + "]";
    }
    
    bool isScaled() const { return isScaled_; }
    std::string getScalingInfo() const { return scalingInfo_; }
    
    void printInfo() const {
        std::cout << "Mesh: " << name_ << std::endl;
        std::cout << "  Triangles: " << triangles_.size() << std::endl;
        std::cout << "  Vertices: " << vertices_.size() << std::endl;
        std::cout << "  Bounding box: [" << bboxMin_[0] << ", " << bboxMin_[1] << ", " << bboxMin_[2] 
                  << "] to [" << bboxMax_[0] << ", " << bboxMax_[1] << ", " << bboxMax_[2] << "]" << std::endl;
        std::cout << "  Total area: " << totalArea_ << std::endl;
        std::cout << "  Scaled: " << (isScaled_ ? "Yes" : "No") << std::endl;
        if(isScaled_) {
            std::cout << "  Scaling info: " << scalingInfo_ << std::endl;
        }
        if(hasPhysicalDimensions_) {
            std::cout << "  Physical dimensions: [" << physicalBBoxMin_[0] << ", " << physicalBBoxMin_[1] << ", " << physicalBBoxMin_[2] 
                      << "] to [" << physicalBBoxMax_[0] << ", " << physicalBBoxMax_[1] << ", " << physicalBBoxMax_[2] << "]" << std::endl;
        }
    }
};

// PhysicalUnitsConverter
class PhysicalUnitsConverter {
public:
    enum class ScalingMode { MANUAL, AUTO_FIT, PRESERVE_ASPECT, TARGET_SIZE };
    
private:
    Real dx_phys_, dt_phys_;
    Vector3<Real> domainSizePhys_;
    Vector3<Uint> domainSizeLattice_;
    Real referenceLength_, referenceVelocity_;
    
public:
    PhysicalUnitsConverter(Real dx, Real dt, const Vector3<Real>& domainPhys) 
        : dx_phys_(dx), dt_phys_(dt), domainSizePhys_(domainPhys), 
          referenceLength_(1.0), referenceVelocity_(1.0) {}
    
    void setDomainSizes(const Vector3<Real>& phys, const Vector3<Uint>& lattice) {
        domainSizePhys_ = phys;
        domainSizeLattice_ = lattice;
    }
    
    void setReferenceValues(Real Lref, Real Uref) {
        referenceLength_ = Lref;
        referenceVelocity_ = Uref;
    }
    
    Real getDx() const { return dx_phys_; }
    
    std::vector<Real> calculateMeshScaling(
        const Vector3<Real>& meshMin, const Vector3<Real>& meshMax,
        const std::vector<Real>& targetRegion, ScalingMode mode) const {
        
        Vector3<Real> meshSize(meshMax[0] - meshMin[0], 
                              meshMax[1] - meshMin[1], 
                              meshMax[2] - meshMin[2]);
        
        Vector3<Real> regionSize(targetRegion[3] - targetRegion[0],
                                targetRegion[4] - targetRegion[1],
                                targetRegion[5] - targetRegion[2]);
        
        Vector3<Real> scaleFactors;
        
        switch(mode) {
            case ScalingMode::PRESERVE_ASPECT: {
                Real scale1 = regionSize[0]/meshSize[0];
                Real scale2 = regionSize[1]/meshSize[1];
                Real scale3 = regionSize[2]/meshSize[2];
                Real minScale = std::min(scale1, std::min(scale2, scale3));
                scaleFactors = Vector3<Real>(minScale, minScale, minScale);
                break;
            }
            case ScalingMode::AUTO_FIT:
                scaleFactors = Vector3<Real>(regionSize[0]/meshSize[0],
                                           regionSize[1]/meshSize[1],
                                           regionSize[2]/meshSize[2]);
                break;
            default:
                scaleFactors = Vector3<Real>(1.0, 1.0, 1.0);
        }
        
        // Calculate offsets to center in target region
        Vector3<Real> scaledSize(meshSize[0] * scaleFactors[0],
                                meshSize[1] * scaleFactors[1],
                                meshSize[2] * scaleFactors[2]);
        
        Vector3<Real> offset((targetRegion[0] + targetRegion[3])/2 - scaledSize[0]/2,
                            (targetRegion[1] + targetRegion[4])/2 - scaledSize[1]/2,
                            (targetRegion[2] + targetRegion[5])/2 - scaledSize[2]/2);
        
        return {scaleFactors[0], scaleFactors[1], scaleFactors[2], 
                offset[0], offset[1], offset[2]};
    }
    
    std::vector<Real> scaleToCharacteristicLength(
        const Vector3<Real>& meshMin, const Vector3<Real>& meshMax,
        Real targetLength, Uint direction = 0) const {
        
        Real currentLength = meshMax[direction] - meshMin[direction];
        Real targetPhysLength = targetLength * dx_phys_;
        Real scaleFactor = targetPhysLength / currentLength;
        
        Vector3<Real> meshCenter((meshMin[0] + meshMax[0])/2,
                                (meshMin[1] + meshMax[1])/2,
                                (meshMin[2] + meshMax[2])/2);
        
        return {scaleFactor, -meshCenter[0] * scaleFactor, 
                -meshCenter[1] * scaleFactor, -meshCenter[2] * scaleFactor};
    }
    
    void printConversionSummary() const {
        std::cout << "Physical Units Converter Summary:\n";
        std::cout << "  dx_phys = " << dx_phys_ << " m\n";
        std::cout << "  dt_phys = " << dt_phys_ << " s\n";
        std::cout << "  Reference length = " << referenceLength_ << " m\n";
        std::cout << "  Reference velocity = " << referenceVelocity_ << " m/s\n";
        std::cout << "  Length scale: 1 lu = " << dx_phys_ << " m\n";
        std::cout << "  Time scale: 1 ts = " << dt_phys_ << " s\n";
        std::cout << "  Velocity scale: 1 lu/ts = " << dx_phys_/dt_phys_ << " m/s\n";
    }
};

void testCylinderScaling() {
    std::cout << "\n=== Testing Cylinder Scaling (Preserve Aspect) ===\n";

    // Create a cylinder-like mesh (diameter = 4cm, height = 10cm)
    TriangleMesh mesh("Cylinder");

    // Add some triangles to represent a cylinder
    Real radius = 0.02; // 2cm radius
    Real height = 0.1;  // 10cm height

    // Add triangles for cylinder (simplified)
    mesh.addTriangle(Vector3<Real>(-radius, -radius, 0), Vector3<Real>(radius, -radius, 0), Vector3<Real>(0, radius, 0));
    mesh.addTriangle(Vector3<Real>(-radius, -radius, height), Vector3<Real>(0, radius, height), Vector3<Real>(radius, -radius, height));
    mesh.finalize();

    // Set physical dimensions
    Vector3<Real> physMin(-radius, -radius, 0);
    Vector3<Real> physMax(radius, radius, height);
    mesh.setPhysicalDimensions(physMin, physMax);

    std::cout << "Original cylinder mesh:\n";
    mesh.printInfo();

    // Setup units converter for typical simulation
    Real dx_phys = 0.00312;  // m (3.12mm)
    Real dt_phys = 0.0005832; // s
    Vector3<Real> domainSizePhys(0.1, 0.2, 0.08); // 10cm x 20cm x 8cm

    PhysicalUnitsConverter converter(dx_phys, dt_phys, domainSizePhys);
    converter.setReferenceValues(0.04, 0.535); // Lref=4cm, Uref=0.535 m/s

    std::cout << "\nUnits converter setup:\n";
    converter.printConversionSummary();

    // Target: cylinder diameter should be 26 lattice units
    Real targetDiameter = 26.0; // lattice units
    auto scalingParams = converter.scaleToCharacteristicLength(
        mesh.getBoundingBoxMin(), mesh.getBoundingBoxMax(), targetDiameter, 0);

    std::cout << "\nScaling parameters for target diameter " << targetDiameter << " lu:\n";
    std::cout << "  Scale factor: " << scalingParams[0] << "\n";
    std::cout << "  Offsets: [" << scalingParams[1] << ", " << scalingParams[2] << ", " << scalingParams[3] << "]\n";

    // Apply scaling
    mesh.applyPhysicalToLatticeScaling(scalingParams);

    std::cout << "\nAfter automatic scaling:\n";
    mesh.printInfo();

    // Verify scaling
    Vector3<Real> newMin = mesh.getBoundingBoxMin();
    Vector3<Real> newMax = mesh.getBoundingBoxMax();
    Real newDiameter = newMax[0] - newMin[0];
    Real expectedDiameterPhys = targetDiameter * dx_phys;

    std::cout << "\nVerification:\n";
    std::cout << "  New diameter (physical): " << newDiameter << " m\n";
    std::cout << "  Expected diameter (physical): " << expectedDiameterPhys << " m\n";
    std::cout << "  Difference: " << std::abs(newDiameter - expectedDiameterPhys) << " m\n";

    assert(std::abs(newDiameter - expectedDiameterPhys) < 1e-6);
    assert(mesh.isScaled());

    std::cout << "✓ Cylinder scaling test passed\n";
}

void testRegionFittingScaling() {
    std::cout << "\n=== Testing Region Fitting Scaling ===\n";

    // Create a cube mesh (2cm x 2cm x 2cm)
    TriangleMesh mesh("Cube");
    mesh.addTriangle(Vector3<Real>(0, 0, 0), Vector3<Real>(0.02, 0, 0), Vector3<Real>(0, 0.02, 0));
    mesh.addTriangle(Vector3<Real>(0.02, 0.02, 0.02), Vector3<Real>(0, 0.02, 0.02), Vector3<Real>(0.02, 0, 0.02));
    mesh.finalize();

    // Set physical dimensions
    mesh.setPhysicalDimensions(Vector3<Real>(0, 0, 0), Vector3<Real>(0.02, 0.02, 0.02));

    std::cout << "Original cube mesh:\n";
    mesh.printInfo();

    // Setup units converter
    PhysicalUnitsConverter converter(0.001, 0.0001, Vector3<Real>(0.1, 0.1, 0.1));

    // Define target region in lattice coordinates
    std::vector<Real> targetRegion = {10, 20, 30, 50, 60, 70}; // [xmin,ymin,zmin,xmax,ymax,zmax]

    std::cout << "\nTarget region: [" << targetRegion[0] << "," << targetRegion[1] << "," << targetRegion[2]
              << "] to [" << targetRegion[3] << "," << targetRegion[4] << "," << targetRegion[5] << "]\n";

    // Test PRESERVE_ASPECT mode
    auto scalingParams = converter.calculateMeshScaling(
        mesh.getBoundingBoxMin(), mesh.getBoundingBoxMax(), targetRegion,
        PhysicalUnitsConverter::ScalingMode::PRESERVE_ASPECT);

    std::cout << "\nPRESERVE_ASPECT scaling parameters:\n";
    std::cout << "  Scale factors: [" << scalingParams[0] << ", " << scalingParams[1] << ", " << scalingParams[2] << "]\n";
    std::cout << "  Offsets: [" << scalingParams[3] << ", " << scalingParams[4] << ", " << scalingParams[5] << "]\n";

    // Apply scaling
    mesh.applyPhysicalToLatticeScaling(scalingParams);

    std::cout << "\nAfter PRESERVE_ASPECT scaling:\n";
    mesh.printInfo();

    // Verify that aspect ratio is preserved (all scale factors should be equal)
    assert(std::abs(scalingParams[0] - scalingParams[1]) < 1e-6);
    assert(std::abs(scalingParams[1] - scalingParams[2]) < 1e-6);
    assert(mesh.isScaled());

    std::cout << "✓ Region fitting scaling test passed\n";
}

void testAutoFitScaling() {
    std::cout << "\n=== Testing Auto-Fit Scaling ===\n";

    // Create a rectangular mesh (4cm x 2cm x 1cm)
    TriangleMesh mesh("Rectangle");
    mesh.addTriangle(Vector3<Real>(0, 0, 0), Vector3<Real>(0.04, 0, 0), Vector3<Real>(0, 0.02, 0));
    mesh.addTriangle(Vector3<Real>(0.04, 0.02, 0.01), Vector3<Real>(0, 0.02, 0.01), Vector3<Real>(0.04, 0, 0.01));
    mesh.finalize();

    std::cout << "Original rectangular mesh:\n";
    mesh.printInfo();

    // Setup units converter
    PhysicalUnitsConverter converter(0.001, 0.0001, Vector3<Real>(0.1, 0.1, 0.1));

    // Define target region
    std::vector<Real> targetRegion = {5, 10, 15, 45, 30, 25}; // Different aspect ratio

    std::cout << "\nTarget region: [" << targetRegion[0] << "," << targetRegion[1] << "," << targetRegion[2]
              << "] to [" << targetRegion[3] << "," << targetRegion[4] << "," << targetRegion[5] << "]\n";

    // Test AUTO_FIT mode
    auto scalingParams = converter.calculateMeshScaling(
        mesh.getBoundingBoxMin(), mesh.getBoundingBoxMax(), targetRegion,
        PhysicalUnitsConverter::ScalingMode::AUTO_FIT);

    std::cout << "\nAUTO_FIT scaling parameters:\n";
    std::cout << "  Scale factors: [" << scalingParams[0] << ", " << scalingParams[1] << ", " << scalingParams[2] << "]\n";
    std::cout << "  Offsets: [" << scalingParams[3] << ", " << scalingParams[4] << ", " << scalingParams[5] << "]\n";

    // Apply scaling
    mesh.applyPhysicalToLatticeScaling(scalingParams);

    std::cout << "\nAfter AUTO_FIT scaling:\n";
    mesh.printInfo();

    // For AUTO_FIT, scale factors can be different (non-uniform scaling)
    assert(mesh.isScaled());

    std::cout << "✓ Auto-fit scaling test passed\n";
}

void testCompleteScalingPipeline() {
    std::cout << "\n=== Testing Complete Scaling Pipeline ===\n";

    // Simulate a real-world scenario: Ahmed body at Re=100
    TriangleMesh mesh("AhmedBody");

    // Ahmed body dimensions: Length=1.044m, Width=0.389m, Height=0.288m
    Real length = 1.044;
    Real width = 0.389;
    Real height = 0.288;

    // Add simplified triangles
    mesh.addTriangle(Vector3<Real>(0, 0, 0), Vector3<Real>(length, 0, 0), Vector3<Real>(0, width, 0));
    mesh.addTriangle(Vector3<Real>(length, width, height), Vector3<Real>(0, width, height), Vector3<Real>(length, 0, height));
    mesh.finalize();

    // Set physical dimensions
    mesh.setPhysicalDimensions(Vector3<Real>(0, 0, 0), Vector3<Real>(length, width, height));

    std::cout << "Original Ahmed body mesh:\n";
    mesh.printInfo();

    // Simulation parameters for Re=100
    Real Uref = 40.0;  // m/s (reference velocity)
    Real Lref = height; // m (characteristic length = height)
    Real nu = 1.5e-5;  // m²/s (kinematic viscosity of air)
    Real Re = 100.0;   // Reynolds number

    // Calculate required viscosity for target Re
    Real nu_target = Uref * Lref / Re;

    // LBM parameters
    Real dx_phys = Lref / 26.0;  // Physical grid spacing (26 cells across height)
    Real dt_phys = dx_phys * dx_phys / (3.0 * nu_target); // Stability constraint

    std::cout << "\nSimulation parameters:\n";
    std::cout << "  Reynolds number: " << Re << "\n";
    std::cout << "  Reference velocity: " << Uref << " m/s\n";
    std::cout << "  Reference length: " << Lref << " m\n";
    std::cout << "  Target viscosity: " << nu_target << " m²/s\n";
    std::cout << "  Grid spacing: " << dx_phys << " m\n";
    std::cout << "  Time step: " << dt_phys << " s\n";

    // Setup units converter
    Vector3<Real> domainSizePhys(5.0 * length, 3.0 * width, 4.0 * height);
    PhysicalUnitsConverter converter(dx_phys, dt_phys, domainSizePhys);
    converter.setReferenceValues(Lref, Uref);

    std::cout << "\nUnits converter setup:\n";
    converter.printConversionSummary();

    // Scale Ahmed body height to 26 lattice units
    Real targetHeight = 26.0; // lattice units
    auto scalingParams = converter.scaleToCharacteristicLength(
        mesh.getBoundingBoxMin(), mesh.getBoundingBoxMax(), targetHeight, 2); // Scale based on Z (height)

    std::cout << "\nScaling parameters for target height " << targetHeight << " lu:\n";
    std::cout << "  Scale factor: " << scalingParams[0] << "\n";
    std::cout << "  Offsets: [" << scalingParams[1] << ", " << scalingParams[2] << ", " << scalingParams[3] << "]\n";

    // Apply scaling
    mesh.applyPhysicalToLatticeScaling(scalingParams);

    std::cout << "\nAfter complete scaling pipeline:\n";
    mesh.printInfo();

    // Verify scaling
    Vector3<Real> newMin = mesh.getBoundingBoxMin();
    Vector3<Real> newMax = mesh.getBoundingBoxMax();
    Real newHeight = newMax[2] - newMin[2];
    Real expectedHeightPhys = targetHeight * dx_phys;

    std::cout << "\nVerification:\n";
    std::cout << "  New height (physical): " << newHeight << " m\n";
    std::cout << "  Expected height (physical): " << expectedHeightPhys << " m\n";
    std::cout << "  Relative error: " << std::abs(newHeight - expectedHeightPhys) / expectedHeightPhys * 100 << "%\n";

    assert(std::abs(newHeight - expectedHeightPhys) < 1e-6);
    assert(mesh.isScaled());

    std::cout << "✓ Complete scaling pipeline test passed\n";
}

int main() {
    std::cout << "Testing Automatic Physical-to-Lattice Scaling Pipeline\n";
    std::cout << "=====================================================\n";

    try {
        testCylinderScaling();
        testRegionFittingScaling();
        testAutoFitScaling();
        testCompleteScalingPipeline();

        std::cout << "\n🎉 All automatic scaling tests passed!\n";
        std::cout << "\nThe enhanced mesh scaling system is working correctly:\n";
        std::cout << "✓ Physical-to-lattice unit conversion\n";
        std::cout << "✓ Multiple scaling modes (PRESERVE_ASPECT, AUTO_FIT)\n";
        std::cout << "✓ Characteristic length scaling\n";
        std::cout << "✓ Region fitting\n";
        std::cout << "✓ Complete simulation pipeline integration\n";

        return 0;
    }
    catch(const std::exception& e) {
        std::cerr << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
}
