//=================================================================================================
/*!
//  \file test_ahmed_body_les.cpp
//  \brief Ahmed body LES validation with enhanced mesh scaling
*/
//=================================================================================================

#include <iostream>
#include <iomanip>
#include <cassert>
#include <cmath>
#include <vector>
#include <string>
#include <fstream>
#include <sstream>
#include <memory>

// Simplified types for testing
typedef double Real;
typedef unsigned int Uint;

template<typename T>
struct Vector3 {
    T data[3];
    Vector3() : data{0, 0, 0} {}
    Vector3(T x, T y, T z) : data{x, y, z} {}
    T& operator[](int i) { return data[i]; }
    const T& operator[](int i) const { return data[i]; }
    Vector3 operator*(T s) const { return Vector3(data[0]*s, data[1]*s, data[2]*s); }
    Vector3 operator+(const Vector3& v) const { return Vector3(data[0]+v[0], data[1]+v[1], data[2]+v[2]); }
    Vector3 operator-(const Vector3& v) const { return Vector3(data[0]-v[0], data[1]-v[1], data[2]-v[2]); }
};

// Ahmed body LES validation parameters
struct AhmedBodyLES {
    // Physical parameters (Ahmed body reference geometry)
    Real length;           // Ahmed body length (m)
    Real width;            // Ahmed body width (m)
    Real height;           // Ahmed body height (m)
    Real slant_angle;      // Rear slant angle (degrees)
    Real Uinf;             // Freestream velocity (m/s)
    Real nu;               // Kinematic viscosity (m²/s)
    Real Re;               // Reynolds number based on height
    Real rho;              // Fluid density (kg/m³)
    
    // Expected results from literature (Lienhart & Becker 2003, Ahmed et al. 1984)
    Real Cd_expected;      // Expected drag coefficient
    Real Cl_expected;      // Expected lift coefficient
    Real Cp_base_expected; // Expected base pressure coefficient
    Real separation_x;     // Expected separation point (x/L)
    
    // Domain parameters for LES
    Real domain_length;    // Domain length (upstream + downstream)
    Real domain_width;     // Domain width
    Real domain_height;    // Domain height
    Real upstream_length;  // Distance from inlet to Ahmed body front
    Real downstream_length; // Distance from Ahmed body rear to outlet
    Real ground_clearance; // Distance from ground to Ahmed body bottom
    
    // LBM-LES parameters
    Real dx_target;        // Target physical grid spacing
    Real dt_target;        // Target physical time step
    Uint height_cells;     // Target Ahmed body height in cells
    Real Ma_target;        // Target Mach number
    Real tau_target;       // Target relaxation time
    Real Cs_smagorinsky;   // Smagorinsky constant
    
    AhmedBodyLES() {
        // Ahmed body reference dimensions (scaled model)
        length = 1.044;                // 1.044 m length
        width = 0.389;                 // 0.389 m width
        height = 0.288;                // 0.288 m height
        slant_angle = 25.0;            // 25° rear slant angle
        
        // Flow parameters for LES validation
        Re = 4.29e6;                   // Reynolds number based on height (high Re for LES)
        Uinf = 40.0;                   // 40 m/s freestream velocity
        nu = Uinf * height / Re;       // Kinematic viscosity
        rho = 1.225;                   // Air density at standard conditions
        
        // Expected results from literature (25° slant angle)
        Cd_expected = 0.285;           // Drag coefficient
        Cl_expected = -0.010;          // Lift coefficient (slight downforce)
        Cp_base_expected = -0.22;      // Base pressure coefficient
        separation_x = 0.38;           // Separation point at x/L = 0.38
        
        // Domain sizing for LES (larger than RANS due to turbulence resolution)
        upstream_length = 3.0 * length;     // 3L upstream
        downstream_length = 8.0 * length;   // 8L downstream
        domain_length = upstream_length + length + downstream_length;
        domain_width = 4.0 * width;         // 4W width
        domain_height = 3.0 * height;       // 3H height
        ground_clearance = 0.05 * height;   // 5% height ground clearance
        
        // LBM-LES parameters for turbulence resolution
        height_cells = 48;                  // Ahmed body height in cells (higher resolution for LES)
        dx_target = height / height_cells;  // Physical grid spacing
        Ma_target = 0.15;                   // Higher Mach number acceptable for LES
        Real cs = 1.0/sqrt(3.0);           // Lattice speed of sound
        Real u_lb = Ma_target * cs;         // Lattice velocity
        dt_target = dx_target * u_lb / Uinf; // Physical time step
        tau_target = 3.0 * nu * dt_target / (dx_target * dx_target) + 0.5; // Relaxation time
        Cs_smagorinsky = 0.17;              // Smagorinsky constant for LES
    }
    
    void printParameters() const {
        std::cout << "Ahmed Body LES Validation Parameters\n";
        std::cout << "====================================\n";
        std::cout << "Physical Parameters:\n";
        std::cout << "  Length: " << length << " m\n";
        std::cout << "  Width: " << width << " m\n";
        std::cout << "  Height: " << height << " m\n";
        std::cout << "  Slant angle: " << slant_angle << "°\n";
        std::cout << "  Reynolds number: " << Re << " (based on height)\n";
        std::cout << "  Freestream velocity: " << Uinf << " m/s\n";
        std::cout << "  Kinematic viscosity: " << nu << " m²/s\n";
        std::cout << "  Fluid density: " << rho << " kg/m³\n\n";
        
        std::cout << "Expected Results (Literature):\n";
        std::cout << "  Drag coefficient: " << Cd_expected << "\n";
        std::cout << "  Lift coefficient: " << Cl_expected << "\n";
        std::cout << "  Base pressure coefficient: " << Cp_base_expected << "\n";
        std::cout << "  Separation point: x/L = " << separation_x << "\n\n";
        
        std::cout << "Domain Parameters:\n";
        std::cout << "  Domain length: " << domain_length << " m (" << domain_length/length << "L)\n";
        std::cout << "  Domain width: " << domain_width << " m (" << domain_width/width << "W)\n";
        std::cout << "  Domain height: " << domain_height << " m (" << domain_height/height << "H)\n";
        std::cout << "  Upstream length: " << upstream_length << " m (" << upstream_length/length << "L)\n";
        std::cout << "  Downstream length: " << downstream_length << " m (" << downstream_length/length << "L)\n";
        std::cout << "  Ground clearance: " << ground_clearance << " m (" << ground_clearance/height << "H)\n\n";
        
        std::cout << "LBM-LES Parameters:\n";
        std::cout << "  Ahmed body height: " << height_cells << " cells\n";
        std::cout << "  Grid spacing: " << dx_target << " m\n";
        std::cout << "  Time step: " << dt_target << " s\n";
        std::cout << "  Target Mach number: " << Ma_target << "\n";
        std::cout << "  Target relaxation time: " << tau_target << "\n";
        std::cout << "  Smagorinsky constant: " << Cs_smagorinsky << "\n";
        std::cout << "  Lattice velocity: " << Ma_target/sqrt(3.0) << " lu/ts\n\n";
    }
    
    Vector3<Uint> getDomainSizeLattice() const {
        return Vector3<Uint>(
            static_cast<Uint>(domain_length / dx_target),
            static_cast<Uint>(domain_width / dx_target),
            static_cast<Uint>(domain_height / dx_target)
        );
    }
    
    Vector3<Real> getDomainSizePhysical() const {
        return Vector3<Real>(domain_length, domain_width, domain_height);
    }
    
    Vector3<Real> getAhmedBodyPosition() const {
        return Vector3<Real>(
            upstream_length,               // X: upstream distance
            domain_width / 2.0 - width / 2.0,  // Y: centered
            ground_clearance               // Z: ground clearance
        );
    }
    
    Vector3<Uint> getAhmedBodyPositionLattice() const {
        Vector3<Real> pos_phys = getAhmedBodyPosition();
        return Vector3<Uint>(
            static_cast<Uint>(pos_phys[0] / dx_target),
            static_cast<Uint>(pos_phys[1] / dx_target),
            static_cast<Uint>(pos_phys[2] / dx_target)
        );
    }
    
    Real getCharacteristicTime() const {
        return height / Uinf;  // Characteristic time scale
    }
    
    Uint getRequiredTimeSteps() const {
        Real total_time = 200.0 * getCharacteristicTime();  // 200 characteristic times for LES statistics
        return static_cast<Uint>(total_time / dt_target);
    }
};

// Simplified mesh and converter classes (reusing from previous implementations)
class TriangleMesh {
private:
    std::vector<Vector3<Real>> vertices_;
    Vector3<Real> bboxMin_, bboxMax_;
    Real totalArea_;
    std::string name_;
    bool isScaled_;
    std::string scalingInfo_;
    
public:
    TriangleMesh(const std::string& name = "Mesh") 
        : totalArea_(0.0), name_(name), isScaled_(false) {}
    
    void setBoundingBox(const Vector3<Real>& min, const Vector3<Real>& max) {
        bboxMin_ = min;
        bboxMax_ = max;
    }
    
    void setArea(Real area) { totalArea_ = area; }
    void setTriangleCount(size_t count) { vertices_.resize(count * 3); }
    
    Vector3<Real> getBoundingBoxMin() const { return bboxMin_; }
    Vector3<Real> getBoundingBoxMax() const { return bboxMax_; }
    Real getTotalArea() const { return totalArea_; }
    size_t getNumTriangles() const { return vertices_.size() / 3; }
    
    void applyPhysicalToLatticeScaling(const std::vector<Real>& scalingParams) {
        if(scalingParams.size() >= 4) {
            Real scaleFactor = scalingParams[0];
            Vector3<Real> offset(scalingParams[1], scalingParams[2], scalingParams[3]);
            
            bboxMin_ = bboxMin_ * scaleFactor + offset;
            bboxMax_ = bboxMax_ * scaleFactor + offset;
            totalArea_ *= scaleFactor * scaleFactor;
            
            isScaled_ = true;
            scalingInfo_ = "Physical-to-lattice scaling applied";
        }
    }
    
    bool isScaled() const { return isScaled_; }
    std::string getScalingInfo() const { return scalingInfo_; }
    
    void printInfo() const {
        std::cout << "Mesh: " << name_ << std::endl;
        std::cout << "  Triangles: " << getNumTriangles() << std::endl;
        std::cout << "  Bounding box: [" << bboxMin_[0] << ", " << bboxMin_[1] << ", " << bboxMin_[2] 
                  << "] to [" << bboxMax_[0] << ", " << bboxMax_[1] << ", " << bboxMax_[2] << "]" << std::endl;
        std::cout << "  Total area: " << totalArea_ << std::endl;
        std::cout << "  Scaled: " << (isScaled_ ? "Yes" : "No") << std::endl;
        if(isScaled_) {
            std::cout << "  Scaling info: " << scalingInfo_ << std::endl;
        }
    }
};

class PhysicalUnitsConverter {
private:
    Real dx_phys_, dt_phys_;
    Vector3<Real> domainSizePhys_;
    Real referenceLength_, referenceVelocity_;
    
public:
    PhysicalUnitsConverter(Real dx, Real dt, const Vector3<Real>& domainPhys) 
        : dx_phys_(dx), dt_phys_(dt), domainSizePhys_(domainPhys), 
          referenceLength_(1.0), referenceVelocity_(1.0) {}
    
    void setReferenceValues(Real Lref, Real Uref) {
        referenceLength_ = Lref;
        referenceVelocity_ = Uref;
    }
    
    Real getDx() const { return dx_phys_; }
    Real getDt() const { return dt_phys_; }
    
    std::vector<Real> scaleToCharacteristicLength(
        const Vector3<Real>& meshMin, const Vector3<Real>& meshMax,
        Real targetLength, Uint direction = 2) const {  // Default to Z-direction for height
        
        Real currentLength = meshMax[direction] - meshMin[direction];
        Real targetPhysLength = targetLength * dx_phys_;
        Real scaleFactor = targetPhysLength / currentLength;
        
        Vector3<Real> meshCenter((meshMin[0] + meshMax[0])/2,
                                (meshMin[1] + meshMax[1])/2,
                                (meshMin[2] + meshMax[2])/2);
        
        return {scaleFactor, -meshCenter[0] * scaleFactor, 
                -meshCenter[1] * scaleFactor, -meshCenter[2] * scaleFactor};
    }
    
    void printConversionSummary() const {
        std::cout << "Physical Units Converter Summary:\n";
        std::cout << "  dx_phys = " << dx_phys_ << " m\n";
        std::cout << "  dt_phys = " << dt_phys_ << " s\n";
        std::cout << "  Reference length = " << referenceLength_ << " m\n";
        std::cout << "  Reference velocity = " << referenceVelocity_ << " m/s\n";
        std::cout << "  Length scale: 1 lu = " << dx_phys_ << " m\n";
        std::cout << "  Time scale: 1 ts = " << dt_phys_ << " s\n";
        std::cout << "  Velocity scale: 1 lu/ts = " << dx_phys_/dt_phys_ << " m/s\n";
    }
};

bool loadAhmedBodySTL(const std::string& filename, TriangleMesh& mesh) {
    // Simulate loading the Ahmed body STL
    if(filename == "ahmed_body.stl") {
        // Set realistic Ahmed body properties
        mesh.setBoundingBox(Vector3<Real>(0.0, 0.0, 0.0), Vector3<Real>(1.044, 0.389, 0.288));
        // Approximate surface area for Ahmed body
        Real surface_area = 2.0 * (1.044 * 0.389 + 1.044 * 0.288 + 0.389 * 0.288) + 0.5 * 1.044 * 0.389; // Including slant
        mesh.setArea(surface_area);
        mesh.setTriangleCount(24); // Simplified Ahmed body with slant
        return true;
    }
    return false;
}

void testAhmedBodyParameters() {
    std::cout << "\n=== Testing Ahmed Body LES Parameters ===\n";

    AhmedBodyLES params;
    params.printParameters();

    // Verify Reynolds number calculation
    Real Re_calculated = params.Uinf * params.height / params.nu;
    assert(std::abs(Re_calculated - params.Re) / params.Re < 1e-6);
    std::cout << "✓ Reynolds number verification: " << Re_calculated << "\n";

    // Verify domain sizing for LES
    assert(params.upstream_length / params.length >= 3.0);   // At least 3L upstream
    assert(params.downstream_length / params.length >= 8.0); // At least 8L downstream
    assert(params.domain_width / params.width >= 4.0);       // At least 4W width
    assert(params.domain_height / params.height >= 3.0);     // At least 3H height
    std::cout << "✓ Domain sizing follows LES best practices\n";

    // Verify LBM-LES parameters are in acceptable range
    assert(params.Ma_target <= 0.2);           // Mach number < 0.2 (higher for LES)
    assert(params.tau_target > 0.5);           // Relaxation time > 0.5
    assert(params.height_cells >= 40);         // High resolution for LES
    assert(params.Cs_smagorinsky > 0.1 && params.Cs_smagorinsky < 0.2); // Typical Smagorinsky range
    std::cout << "✓ LBM-LES parameters are in acceptable range\n";

    // Verify Ahmed body specific characteristics
    assert(params.slant_angle == 25.0);        // 25° slant angle
    assert(params.Cd_expected > 0.25);         // Reasonable drag coefficient
    assert(params.separation_x > 0.3 && params.separation_x < 0.5); // Separation in expected range
    std::cout << "✓ Ahmed body specific characteristics verified\n";

    std::cout << "✓ Ahmed body parameters test passed\n";
}

void testMeshScalingForAhmedBody() {
    std::cout << "\n=== Testing Mesh Scaling for Ahmed Body ===\n";

    AhmedBodyLES params;

    // Load Ahmed body mesh
    TriangleMesh mesh("AhmedBody");
    bool success = loadAhmedBodySTL("ahmed_body.stl", mesh);
    assert(success);

    std::cout << "Original Ahmed body mesh:\n";
    mesh.printInfo();

    // Setup units converter
    PhysicalUnitsConverter converter(params.dx_target, params.dt_target, params.getDomainSizePhysical());
    converter.setReferenceValues(params.height, params.Uinf);

    std::cout << "\nUnits converter setup:\n";
    converter.printConversionSummary();

    // Scale Ahmed body height to target cells
    auto scalingParams = converter.scaleToCharacteristicLength(
        mesh.getBoundingBoxMin(), mesh.getBoundingBoxMax(),
        params.height_cells, 2); // Scale based on Z-direction (height)

    std::cout << "\nScaling parameters for " << params.height_cells << " cells height:\n";
    std::cout << "  Scale factor: " << scalingParams[0] << "\n";
    std::cout << "  Offsets: [" << scalingParams[1] << ", " << scalingParams[2] << ", " << scalingParams[3] << "]\n";

    // Apply scaling
    mesh.applyPhysicalToLatticeScaling(scalingParams);

    std::cout << "\nAfter scaling:\n";
    mesh.printInfo();

    // Verify scaling accuracy
    Vector3<Real> meshMin = mesh.getBoundingBoxMin();
    Vector3<Real> meshMax = mesh.getBoundingBoxMax();
    Real scaledHeight = meshMax[2] - meshMin[2];
    Real expectedHeight = params.height_cells * params.dx_target;

    std::cout << "\nScaling verification:\n";
    std::cout << "  Scaled height: " << scaledHeight << " m\n";
    std::cout << "  Expected height: " << expectedHeight << " m\n";
    std::cout << "  Height in lattice units: " << scaledHeight / params.dx_target << " lu\n";
    std::cout << "  Target lattice units: " << params.height_cells << " lu\n";

    Real heightError = std::abs(scaledHeight / params.dx_target - params.height_cells);
    assert(heightError < 0.1); // Within 0.1 lattice units

    // Verify aspect ratios are preserved
    Real scaledLength = meshMax[0] - meshMin[0];
    Real scaledWidth = meshMax[1] - meshMin[1];
    Real originalAspectRatio_LH = params.length / params.height;
    Real originalAspectRatio_WH = params.width / params.height;
    Real scaledAspectRatio_LH = scaledLength / scaledHeight;
    Real scaledAspectRatio_WH = scaledWidth / scaledHeight;

    std::cout << "  Original L/H ratio: " << originalAspectRatio_LH << "\n";
    std::cout << "  Scaled L/H ratio: " << scaledAspectRatio_LH << "\n";
    std::cout << "  Original W/H ratio: " << originalAspectRatio_WH << "\n";
    std::cout << "  Scaled W/H ratio: " << scaledAspectRatio_WH << "\n";

    assert(std::abs(originalAspectRatio_LH - scaledAspectRatio_LH) < 0.01);
    assert(std::abs(originalAspectRatio_WH - scaledAspectRatio_WH) < 0.01);

    std::cout << "✓ Mesh scaling test passed\n";
}

void testLESRequirements() {
    std::cout << "\n=== Testing LES Requirements ===\n";

    AhmedBodyLES params;

    // Calculate grid resolution requirements
    Real y_plus_target = 1.0;  // Target y+ for wall-resolved LES
    Real Re_tau = 0.09 * pow(params.Re, 0.88);  // Approximate friction Reynolds number
    Real u_tau = params.Uinf * sqrt(0.5 * 0.005);  // Approximate friction velocity (Cf ~ 0.005)
    Real delta_y_plus = y_plus_target * params.nu / u_tau;  // Required first cell height

    std::cout << "LES Grid Requirements:\n";
    std::cout << "  Target y+: " << y_plus_target << "\n";
    std::cout << "  Estimated Re_tau: " << Re_tau << "\n";
    std::cout << "  Estimated u_tau: " << u_tau << " m/s\n";
    std::cout << "  Required first cell height: " << delta_y_plus << " m\n";
    std::cout << "  Current grid spacing: " << params.dx_target << " m\n";
    std::cout << "  Grid spacing ratio: " << params.dx_target / delta_y_plus << "\n";

    // Check if grid is fine enough for wall-resolved LES
    bool wall_resolved = (params.dx_target <= 2.0 * delta_y_plus);
    std::cout << "  Wall-resolved LES: " << (wall_resolved ? "Yes" : "No (wall-modeled)") << "\n";

    // Calculate time step requirements
    Real CFL_target = 0.3;  // Conservative CFL for LES
    Real dt_CFL = CFL_target * params.dx_target / params.Uinf;
    std::cout << "\nTime Step Requirements:\n";
    std::cout << "  Target CFL: " << CFL_target << "\n";
    std::cout << "  Required dt (CFL): " << dt_CFL << " s\n";
    std::cout << "  Current dt: " << params.dt_target << " s\n";
    std::cout << "  Actual CFL: " << params.Uinf * params.dt_target / params.dx_target << "\n";

    assert(params.Uinf * params.dt_target / params.dx_target <= 0.5); // CFL check

    // Calculate computational requirements
    Vector3<Uint> domainSize = params.getDomainSizeLattice();
    Uint totalCells = domainSize[0] * domainSize[1] * domainSize[2];
    Uint timeSteps = params.getRequiredTimeSteps();
    Real characteristicTime = params.getCharacteristicTime();

    std::cout << "\nComputational Requirements:\n";
    std::cout << "  Domain size: " << domainSize[0] << "x" << domainSize[1] << "x" << domainSize[2] << " cells\n";
    std::cout << "  Total cells: " << totalCells << "\n";
    std::cout << "  Characteristic time: " << characteristicTime << " s\n";
    std::cout << "  Required time steps: " << timeSteps << "\n";
    std::cout << "  Total simulation time: " << timeSteps * params.dt_target << " s\n";
    std::cout << "  Simulation periods: " << (timeSteps * params.dt_target) / characteristicTime << "\n";

    assert(timeSteps > 100000); // Sufficient time steps for statistics

    std::cout << "✓ LES requirements test passed\n";
}

void testCompleteAhmedBodyWorkflow() {
    std::cout << "\n=== Testing Complete Ahmed Body LES Workflow ===\n";

    AhmedBodyLES params;

    std::cout << "Setting up complete Ahmed body LES validation case...\n";

    // Step 1: Load and scale mesh
    TriangleMesh mesh("AhmedBody_LES");
    bool success = loadAhmedBodySTL("ahmed_body.stl", mesh);
    assert(success);

    PhysicalUnitsConverter converter(params.dx_target, params.dt_target, params.getDomainSizePhysical());
    converter.setReferenceValues(params.height, params.Uinf);

    auto scalingParams = converter.scaleToCharacteristicLength(
        mesh.getBoundingBoxMin(), mesh.getBoundingBoxMax(), params.height_cells, 2);
    mesh.applyPhysicalToLatticeScaling(scalingParams);

    // Step 2: Position mesh in domain
    Vector3<Real> ahmedPos = params.getAhmedBodyPosition();
    std::cout << "Ahmed body positioned at: [" << ahmedPos[0] << ", " << ahmedPos[1] << ", " << ahmedPos[2] << "] m\n";

    // Step 3: Verify simulation parameters
    std::cout << "\nLES simulation ready with parameters:\n";
    std::cout << "  Reynolds number: " << params.Re << " (based on height)\n";
    std::cout << "  Domain: " << params.getDomainSizeLattice()[0] << "x" << params.getDomainSizeLattice()[1] << "x" << params.getDomainSizeLattice()[2] << " cells\n";
    std::cout << "  Ahmed body height: " << params.height_cells << " cells\n";
    std::cout << "  Mach number: " << params.Ma_target << "\n";
    std::cout << "  Relaxation time: " << params.tau_target << "\n";
    std::cout << "  Smagorinsky constant: " << params.Cs_smagorinsky << "\n";

    // Step 4: Expected results and validation targets
    std::cout << "\nExpected validation results:\n";
    std::cout << "  Drag coefficient: " << params.Cd_expected << " ± 0.02\n";
    std::cout << "  Lift coefficient: " << params.Cl_expected << " ± 0.005\n";
    std::cout << "  Base pressure coefficient: " << params.Cp_base_expected << " ± 0.02\n";
    std::cout << "  Separation point: x/L = " << params.separation_x << " ± 0.02\n";

    // Step 5: LES-specific validation considerations
    std::cout << "\nLES validation considerations:\n";
    std::cout << "  - Turbulent flow with complex 3D separation\n";
    std::cout << "  - Requires statistical averaging over multiple flow-through times\n";
    std::cout << "  - Sensitive to rear slant angle (25° configuration)\n";
    std::cout << "  - Ground effect and boundary layer interaction important\n";
    std::cout << "  - Wake structure and pressure recovery critical\n";

    // Step 6: Computational requirements
    Vector3<Uint> domainSize = params.getDomainSizeLattice();
    Uint totalCells = domainSize[0] * domainSize[1] * domainSize[2];
    Uint timeSteps = params.getRequiredTimeSteps();

    std::cout << "\nComputational requirements:\n";
    std::cout << "  Total cells: " << totalCells << " (~" << totalCells/1e6 << "M cells)\n";
    std::cout << "  Time steps: " << timeSteps << " (~" << timeSteps/1e6 << "M steps)\n";
    std::cout << "  Estimated runtime: High (LES requires significant computational resources)\n";

    // Verify all parameters are reasonable
    assert(mesh.isScaled());
    assert(params.Re > 1e6);  // High Reynolds number for LES
    assert(params.Ma_target <= 0.2);
    assert(params.tau_target > 0.5);
    assert(totalCells > 1e6);  // Sufficient resolution for LES

    std::cout << "✓ Complete Ahmed body LES workflow test passed\n";
    std::cout << "🚀 Ready for Ahmed body LES validation!\n";
}

void testTurbulenceModelValidation() {
    std::cout << "\n=== Testing Turbulence Model Validation Framework ===\n";

    AhmedBodyLES params;

    std::cout << "Turbulence model validation framework for Ahmed body:\n";

    // Define validation metrics
    struct ValidationMetric {
        std::string name;
        Real expected;
        Real tolerance;
        std::string description;
    };

    std::vector<ValidationMetric> metrics = {
        {"Cd", params.Cd_expected, 0.02, "Drag coefficient"},
        {"Cl", params.Cl_expected, 0.005, "Lift coefficient"},
        {"Cp_base", params.Cp_base_expected, 0.02, "Base pressure coefficient"},
        {"x_sep/L", params.separation_x, 0.02, "Separation point"},
        {"St", 0.17, 0.01, "Strouhal number (if periodic)"},
        {"Cp_roof", -0.15, 0.02, "Roof pressure coefficient"},
        {"Cp_rear", -0.25, 0.03, "Rear window pressure coefficient"}
    };

    std::cout << "\nValidation metrics:\n";
    std::cout << "Metric        Expected    Tolerance   Description\n";
    std::cout << "------        --------    ---------   -----------\n";
    for(const auto& metric : metrics) {
        std::cout << std::setw(12) << metric.name
                  << std::setw(12) << metric.expected
                  << std::setw(12) << metric.tolerance
                  << "  " << metric.description << "\n";
    }

    // Define measurement locations
    std::cout << "\nKey measurement locations:\n";
    std::cout << "  - Pressure taps along centerline\n";
    std::cout << "  - Wake velocity profiles at x/L = 1.2, 1.5, 2.0\n";
    std::cout << "  - Surface pressure on rear slant\n";
    std::cout << "  - Base pressure at rear face\n";
    std::cout << "  - Separation line on rear slant\n";

    // Define post-processing requirements
    std::cout << "\nPost-processing requirements:\n";
    std::cout << "  - Time averaging over at least 10 flow-through times\n";
    std::cout << "  - Statistical convergence monitoring\n";
    std::cout << "  - Spectral analysis for unsteady phenomena\n";
    std::cout << "  - Comparison with experimental data (Lienhart & Becker 2003)\n";
    std::cout << "  - Validation against other LES/DNS results\n";

    // Verify framework completeness
    assert(metrics.size() >= 5);  // Sufficient validation metrics
    assert(params.getRequiredTimeSteps() > 100000);  // Sufficient time for statistics

    std::cout << "✓ Turbulence model validation framework test passed\n";
}

int main() {
    std::cout << "Testing Ahmed Body LES Validation Case\n";
    std::cout << "======================================\n";

    try {
        testAhmedBodyParameters();
        testMeshScalingForAhmedBody();
        testLESRequirements();
        testCompleteAhmedBodyWorkflow();
        testTurbulenceModelValidation();

        std::cout << "\n🎉 All Ahmed body LES tests passed!\n";
        std::cout << "\nThe Ahmed body LES validation case is properly configured:\n";
        std::cout << "✓ High Reynolds number LES parameters validated\n";
        std::cout << "✓ Mesh scaling system working correctly\n";
        std::cout << "✓ Domain setup follows LES best practices\n";
        std::cout << "✓ LBM-LES parameters in acceptable range\n";
        std::cout << "✓ Expected results defined for validation\n";
        std::cout << "✓ Turbulence model validation framework established\n";
        std::cout << "✓ Computational requirements assessed\n";

        std::cout << "\n🔬 Ready for advanced turbulence model validation!\n";

        return 0;
    }
    catch(const std::exception& e) {
        std::cerr << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
}
