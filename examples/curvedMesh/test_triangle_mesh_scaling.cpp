//=================================================================================================
/*!
//  \file test_triangle_mesh_scaling.cpp
//  \brief Test TriangleMesh enhanced scaling methods
*/
//=================================================================================================

#include <iostream>
#include <iomanip>
#include <cassert>
#include <cmath>
#include <vector>
#include <string>

// Simplified types for testing
typedef double Real;
typedef unsigned int Uint;

template<typename T>
struct Vector3 {
    T data[3];
    Vector3() : data{0, 0, 0} {}
    Vector3(T x, T y, T z) : data{x, y, z} {}
    T& operator[](int i) { return data[i]; }
    const T& operator[](int i) const { return data[i]; }
    Vector3 operator*(T s) const { return Vector3(data[0]*s, data[1]*s, data[2]*s); }
    Vector3 operator+(const Vector3& v) const { return Vector3(data[0]+v[0], data[1]+v[1], data[2]+v[2]); }
    Vector3 operator-(const Vector3& v) const { return Vector3(data[0]-v[0], data[1]-v[1], data[2]-v[2]); }
};

// Simplified Triangle structure
struct Triangle {
    Vector3<Real> vertices[3];
    Vector3<Real> normal;
    Real area;
    
    Triangle() : area(0.0) {}
    Triangle(const Vector3<Real>& v0, const Vector3<Real>& v1, const Vector3<Real>& v2) {
        vertices[0] = v0;
        vertices[1] = v1;
        vertices[2] = v2;
        computeProperties();
    }
    
    void computeProperties() {
        Vector3<Real> edge1 = vertices[1] - vertices[0];
        Vector3<Real> edge2 = vertices[2] - vertices[0];
        
        // Cross product for normal
        normal[0] = edge1[1] * edge2[2] - edge1[2] * edge2[1];
        normal[1] = edge1[2] * edge2[0] - edge1[0] * edge2[2];
        normal[2] = edge1[0] * edge2[1] - edge1[1] * edge2[0];
        
        // Area = 0.5 * |cross product|
        Real length = std::sqrt(normal[0]*normal[0] + normal[1]*normal[1] + normal[2]*normal[2]);
        area = 0.5 * length;
        
        // Normalize normal
        if(length > 1e-12) {
            normal[0] /= length;
            normal[1] /= length;
            normal[2] /= length;
        }
    }
};

// Simplified TriangleMesh for testing
class TriangleMesh {
private:
    std::vector<Vector3<Real>> vertices_;
    std::vector<Vector3<Real>> originalVertices_;
    std::vector<Triangle> triangles_;
    Vector3<Real> bboxMin_, bboxMax_;
    bool hasPhysicalDimensions_;
    Vector3<Real> physicalBBoxMin_, physicalBBoxMax_;
    bool isScaled_;
    std::string scalingInfo_;
    Real totalArea_;
    std::string name_;
    
public:
    TriangleMesh(const std::string& name = "TestMesh") 
        : hasPhysicalDimensions_(false), isScaled_(false), totalArea_(0.0), name_(name) {}
    
    void addTriangle(const Vector3<Real>& v0, const Vector3<Real>& v1, const Vector3<Real>& v2) {
        triangles_.emplace_back(v0, v1, v2);
        
        // Add vertices if not already present
        vertices_.push_back(v0);
        vertices_.push_back(v1);
        vertices_.push_back(v2);
    }
    
    void finalize() {
        computeBoundingBox();
        computeTotalArea();
    }
    
    void computeBoundingBox() {
        if(vertices_.empty()) return;
        
        bboxMin_ = bboxMax_ = vertices_[0];
        for(const auto& v : vertices_) {
            for(int i = 0; i < 3; ++i) {
                bboxMin_[i] = std::min(bboxMin_[i], v[i]);
                bboxMax_[i] = std::max(bboxMax_[i], v[i]);
            }
        }
    }
    
    void computeTotalArea() {
        totalArea_ = 0.0;
        for(const auto& tri : triangles_) {
            totalArea_ += tri.area;
        }
    }
    
    Vector3<Real> getBoundingBoxMin() const { return bboxMin_; }
    Vector3<Real> getBoundingBoxMax() const { return bboxMax_; }
    Real getTotalArea() const { return totalArea_; }
    size_t getNumTriangles() const { return triangles_.size(); }
    
    // Enhanced scaling methods
    void scaleUniform(Real scaleFactor, const Vector3<Real>& offset = Vector3<Real>(0,0,0)) {
        if(scaleFactor <= 0.0) {
            std::cerr << "Error: Scale factor must be positive" << std::endl;
            return;
        }
        
        // Store original vertices if not already stored
        if(originalVertices_.empty()) {
            originalVertices_ = vertices_;
        }
        
        // Scale and translate all vertices
        for(auto& vertex : vertices_) {
            vertex = vertex * scaleFactor + offset;
        }
        
        // Update triangle properties
        for(auto& triangle : triangles_) {
            for(int i = 0; i < 3; ++i) {
                triangle.vertices[i] = triangle.vertices[i] * scaleFactor + offset;
            }
            triangle.computeProperties();
        }
        
        // Update bounding box and area
        computeBoundingBox();
        totalArea_ *= scaleFactor * scaleFactor;
        
        isScaled_ = true;
        scalingInfo_ = "Uniform scaling: " + std::to_string(scaleFactor);
    }
    
    void scaleNonUniform(const Vector3<Real>& scaleFactors, const Vector3<Real>& offset = Vector3<Real>(0,0,0)) {
        if(scaleFactors[0] <= 0.0 || scaleFactors[1] <= 0.0 || scaleFactors[2] <= 0.0) {
            std::cerr << "Error: Scale factors must be positive" << std::endl;
            return;
        }
        
        // Store original vertices if not already stored
        if(originalVertices_.empty()) {
            originalVertices_ = vertices_;
        }
        
        // Scale and translate all vertices
        for(auto& vertex : vertices_) {
            vertex[0] = vertex[0] * scaleFactors[0] + offset[0];
            vertex[1] = vertex[1] * scaleFactors[1] + offset[1];
            vertex[2] = vertex[2] * scaleFactors[2] + offset[2];
        }
        
        // Update triangle properties
        for(auto& triangle : triangles_) {
            for(int i = 0; i < 3; ++i) {
                triangle.vertices[i][0] = triangle.vertices[i][0] * scaleFactors[0] + offset[0];
                triangle.vertices[i][1] = triangle.vertices[i][1] * scaleFactors[1] + offset[1];
                triangle.vertices[i][2] = triangle.vertices[i][2] * scaleFactors[2] + offset[2];
            }
            triangle.computeProperties();
        }
        
        // Update bounding box
        computeBoundingBox();
        
        // Update area (approximate for non-uniform scaling)
        Real areaScaleFactor = std::sqrt(scaleFactors[0] * scaleFactors[1] * scaleFactors[0] * scaleFactors[2] * scaleFactors[1] * scaleFactors[2]) / 3.0;
        totalArea_ *= areaScaleFactor;
        
        isScaled_ = true;
        scalingInfo_ = "Non-uniform scaling: [" + std::to_string(scaleFactors[0]) + ", " + 
                       std::to_string(scaleFactors[1]) + ", " + std::to_string(scaleFactors[2]) + "]";
    }
    
    void fitToBoundingBox(const Vector3<Real>& targetMin, const Vector3<Real>& targetMax) {
        Vector3<Real> currentSize = bboxMax_ - bboxMin_;
        Vector3<Real> targetSize = targetMax - targetMin;
        
        Vector3<Real> scaleFactors(targetSize[0] / currentSize[0],
                                  targetSize[1] / currentSize[1],
                                  targetSize[2] / currentSize[2]);
        
        Vector3<Real> offset = targetMin - Vector3<Real>(bboxMin_[0] * scaleFactors[0],
                                                        bboxMin_[1] * scaleFactors[1],
                                                        bboxMin_[2] * scaleFactors[2]);
        
        scaleNonUniform(scaleFactors, offset);
        scalingInfo_ = "Fit to bounding box";
    }
    
    void applyPhysicalToLatticeScaling(const std::vector<Real>& scalingParams) {
        if(scalingParams.size() == 4) {
            // Uniform scaling: [scale_factor, offset_x, offset_y, offset_z]
            scaleUniform(scalingParams[0], Vector3<Real>(scalingParams[1], scalingParams[2], scalingParams[3]));
        } else if(scalingParams.size() >= 6) {
            // Non-uniform scaling: [scale_x, scale_y, scale_z, offset_x, offset_y, offset_z]
            Vector3<Real> scaleFactors(scalingParams[0], scalingParams[1], scalingParams[2]);
            Vector3<Real> offset(scalingParams[3], scalingParams[4], scalingParams[5]);
            scaleNonUniform(scaleFactors, offset);
        }
        
        isScaled_ = true;
        scalingInfo_ = "Physical-to-lattice scaling applied";
    }
    
    void setPhysicalDimensions(const Vector3<Real>& physMin, const Vector3<Real>& physMax) {
        physicalBBoxMin_ = physMin;
        physicalBBoxMax_ = physMax;
        hasPhysicalDimensions_ = true;
    }
    
    bool getPhysicalDimensions(Vector3<Real>& physMin, Vector3<Real>& physMax) const {
        if(hasPhysicalDimensions_) {
            physMin = physicalBBoxMin_;
            physMax = physicalBBoxMax_;
            return true;
        }
        return false;
    }
    
    bool isScaled() const { return isScaled_; }
    std::string getScalingInfo() const { return scalingInfo_; }
    
    void printInfo() const {
        std::cout << "Mesh: " << name_ << std::endl;
        std::cout << "  Triangles: " << triangles_.size() << std::endl;
        std::cout << "  Vertices: " << vertices_.size() << std::endl;
        std::cout << "  Bounding box: [" << bboxMin_[0] << ", " << bboxMin_[1] << ", " << bboxMin_[2] 
                  << "] to [" << bboxMax_[0] << ", " << bboxMax_[1] << ", " << bboxMax_[2] << "]" << std::endl;
        std::cout << "  Total area: " << totalArea_ << std::endl;
        std::cout << "  Scaled: " << (isScaled_ ? "Yes" : "No") << std::endl;
        if(isScaled_) {
            std::cout << "  Scaling info: " << scalingInfo_ << std::endl;
        }
        if(hasPhysicalDimensions_) {
            std::cout << "  Physical dimensions: [" << physicalBBoxMin_[0] << ", " << physicalBBoxMin_[1] << ", " << physicalBBoxMin_[2] 
                      << "] to [" << physicalBBoxMax_[0] << ", " << physicalBBoxMax_[1] << ", " << physicalBBoxMax_[2] << "]" << std::endl;
        }
    }
};

void testUniformScaling() {
    std::cout << "\n=== Testing Uniform Scaling ===\n";

    // Create a simple cube mesh
    TriangleMesh mesh("TestCube");

    // Add two triangles to form a simple shape
    mesh.addTriangle(Vector3<Real>(0, 0, 0), Vector3<Real>(1, 0, 0), Vector3<Real>(0, 1, 0));
    mesh.addTriangle(Vector3<Real>(1, 1, 1), Vector3<Real>(0, 1, 1), Vector3<Real>(1, 0, 1));
    mesh.finalize();

    std::cout << "Original mesh:\n";
    mesh.printInfo();

    Vector3<Real> originalMin = mesh.getBoundingBoxMin();
    Vector3<Real> originalMax = mesh.getBoundingBoxMax();
    Real originalArea = mesh.getTotalArea();

    // Apply uniform scaling
    Real scaleFactor = 2.0;
    Vector3<Real> offset(10.0, 20.0, 30.0);
    mesh.scaleUniform(scaleFactor, offset);

    std::cout << "\nAfter uniform scaling (factor=" << scaleFactor << ", offset=[10,20,30]):\n";
    mesh.printInfo();

    // Verify scaling
    Vector3<Real> newMin = mesh.getBoundingBoxMin();
    Vector3<Real> newMax = mesh.getBoundingBoxMax();
    Real newArea = mesh.getTotalArea();

    // Check that dimensions are scaled correctly
    Vector3<Real> expectedMin = originalMin * scaleFactor + offset;
    Vector3<Real> expectedMax = originalMax * scaleFactor + offset;
    Real expectedArea = originalArea * scaleFactor * scaleFactor;

    for(int i = 0; i < 3; ++i) {
        assert(std::abs(newMin[i] - expectedMin[i]) < 1e-10);
        assert(std::abs(newMax[i] - expectedMax[i]) < 1e-10);
    }
    assert(std::abs(newArea - expectedArea) < 1e-10);
    assert(mesh.isScaled());

    std::cout << "✓ Uniform scaling test passed\n";
}

void testNonUniformScaling() {
    std::cout << "\n=== Testing Non-Uniform Scaling ===\n";

    // Create a simple mesh
    TriangleMesh mesh("TestNonUniform");
    mesh.addTriangle(Vector3<Real>(0, 0, 0), Vector3<Real>(2, 0, 0), Vector3<Real>(0, 2, 0));
    mesh.addTriangle(Vector3<Real>(2, 2, 2), Vector3<Real>(0, 2, 2), Vector3<Real>(2, 0, 2));
    mesh.finalize();

    std::cout << "Original mesh:\n";
    mesh.printInfo();

    Vector3<Real> originalMin = mesh.getBoundingBoxMin();
    Vector3<Real> originalMax = mesh.getBoundingBoxMax();

    // Apply non-uniform scaling
    Vector3<Real> scaleFactors(3.0, 0.5, 2.0);  // Stretch X, compress Y, double Z
    Vector3<Real> offset(5.0, 10.0, 15.0);
    mesh.scaleNonUniform(scaleFactors, offset);

    std::cout << "\nAfter non-uniform scaling (factors=[3,0.5,2], offset=[5,10,15]):\n";
    mesh.printInfo();

    // Verify scaling
    Vector3<Real> newMin = mesh.getBoundingBoxMin();
    Vector3<Real> newMax = mesh.getBoundingBoxMax();

    // Check that dimensions are scaled correctly
    Vector3<Real> expectedMin(originalMin[0] * scaleFactors[0] + offset[0],
                             originalMin[1] * scaleFactors[1] + offset[1],
                             originalMin[2] * scaleFactors[2] + offset[2]);
    Vector3<Real> expectedMax(originalMax[0] * scaleFactors[0] + offset[0],
                             originalMax[1] * scaleFactors[1] + offset[1],
                             originalMax[2] * scaleFactors[2] + offset[2]);

    for(int i = 0; i < 3; ++i) {
        assert(std::abs(newMin[i] - expectedMin[i]) < 1e-10);
        assert(std::abs(newMax[i] - expectedMax[i]) < 1e-10);
    }
    assert(mesh.isScaled());

    std::cout << "✓ Non-uniform scaling test passed\n";
}

void testFitToBoundingBox() {
    std::cout << "\n=== Testing Fit to Bounding Box ===\n";

    // Create a mesh with known dimensions
    TriangleMesh mesh("TestFit");
    mesh.addTriangle(Vector3<Real>(1, 1, 1), Vector3<Real>(3, 1, 1), Vector3<Real>(1, 3, 1));
    mesh.addTriangle(Vector3<Real>(3, 3, 3), Vector3<Real>(1, 3, 3), Vector3<Real>(3, 1, 3));
    mesh.finalize();

    std::cout << "Original mesh:\n";
    mesh.printInfo();

    // Define target bounding box
    Vector3<Real> targetMin(10, 20, 30);
    Vector3<Real> targetMax(50, 60, 90);

    mesh.fitToBoundingBox(targetMin, targetMax);

    std::cout << "\nAfter fitting to bounding box [10,20,30] to [50,60,90]:\n";
    mesh.printInfo();

    // Verify that mesh fits exactly in target box
    Vector3<Real> newMin = mesh.getBoundingBoxMin();
    Vector3<Real> newMax = mesh.getBoundingBoxMax();

    for(int i = 0; i < 3; ++i) {
        assert(std::abs(newMin[i] - targetMin[i]) < 1e-10);
        assert(std::abs(newMax[i] - targetMax[i]) < 1e-10);
    }
    assert(mesh.isScaled());

    std::cout << "✓ Fit to bounding box test passed\n";
}

void testPhysicalDimensions() {
    std::cout << "\n=== Testing Physical Dimensions Tracking ===\n";

    TriangleMesh mesh("TestPhysical");
    mesh.addTriangle(Vector3<Real>(0, 0, 0), Vector3<Real>(1, 0, 0), Vector3<Real>(0, 1, 0));
    mesh.finalize();

    std::cout << "Original mesh:\n";
    mesh.printInfo();

    // Set physical dimensions
    Vector3<Real> physMin(0.0, 0.0, 0.0);  // meters
    Vector3<Real> physMax(0.04, 0.04, 0.04);  // 4cm cube
    mesh.setPhysicalDimensions(physMin, physMax);

    std::cout << "\nAfter setting physical dimensions:\n";
    mesh.printInfo();

    // Verify physical dimensions can be retrieved
    Vector3<Real> retrievedMin, retrievedMax;
    bool hasPhys = mesh.getPhysicalDimensions(retrievedMin, retrievedMax);

    assert(hasPhys);
    for(int i = 0; i < 3; ++i) {
        assert(std::abs(retrievedMin[i] - physMin[i]) < 1e-10);
        assert(std::abs(retrievedMax[i] - physMax[i]) < 1e-10);
    }

    std::cout << "✓ Physical dimensions tracking test passed\n";
}

void testPhysicalToLatticeScaling() {
    std::cout << "\n=== Testing Physical-to-Lattice Scaling ===\n";

    TriangleMesh mesh("TestPhysToLattice");
    mesh.addTriangle(Vector3<Real>(0, 0, 0), Vector3<Real>(1, 0, 0), Vector3<Real>(0, 1, 0));
    mesh.finalize();

    std::cout << "Original mesh:\n";
    mesh.printInfo();

    // Test uniform scaling parameters
    std::vector<Real> uniformParams = {2.0, 10.0, 20.0, 30.0};  // [scale, offset_x, offset_y, offset_z]
    mesh.applyPhysicalToLatticeScaling(uniformParams);

    std::cout << "\nAfter physical-to-lattice scaling (uniform):\n";
    mesh.printInfo();

    assert(mesh.isScaled());

    // Create another mesh for non-uniform test
    TriangleMesh mesh2("TestPhysToLattice2");
    mesh2.addTriangle(Vector3<Real>(0, 0, 0), Vector3<Real>(1, 0, 0), Vector3<Real>(0, 1, 0));
    mesh2.finalize();

    // Test non-uniform scaling parameters
    std::vector<Real> nonUniformParams = {2.0, 1.5, 3.0, 5.0, 10.0, 15.0};  // [scale_x, scale_y, scale_z, offset_x, offset_y, offset_z]
    mesh2.applyPhysicalToLatticeScaling(nonUniformParams);

    std::cout << "\nSecond mesh after physical-to-lattice scaling (non-uniform):\n";
    mesh2.printInfo();

    assert(mesh2.isScaled());

    std::cout << "✓ Physical-to-lattice scaling test passed\n";
}

int main() {
    std::cout << "Testing TriangleMesh Enhanced Scaling Methods\n";
    std::cout << "=============================================\n";

    try {
        testUniformScaling();
        testNonUniformScaling();
        testFitToBoundingBox();
        testPhysicalDimensions();
        testPhysicalToLatticeScaling();

        std::cout << "\n🎉 All TriangleMesh scaling tests passed!\n";
        return 0;
    }
    catch(const std::exception& e) {
        std::cerr << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
}
