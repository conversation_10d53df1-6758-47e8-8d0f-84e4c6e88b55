//=================================================================================================
// Real waLBerla Parameter File for Enhanced Mesh Scaling Validation
// Uses actual waLBerla syntax and interfaces - no mock components
//=================================================================================================

Domain {
    cells < 260, 130, 26 >;        // 10D x 5D x 1D for cylinder (D=26 cells)
    size < 1.0, 0.5, 0.1 >;        // Physical domain size in meters
    periodic < 0, 0, 1 >;          // Periodic in Z-direction only
}

Physics {
    // Physical parameters for Re=100 cylinder validation
    kinematicViscosity  1.5e-5;    // m²/s (air at room temperature)
    density            1.225;      // kg/m³ (air density)
    referenceVelocity  0.535;      // m/s (to achieve Re=100 with D=0.04m)
    referenceLength    0.04;       // m (cylinder diameter)
    
    // LBM parameters
    relaxationTime     0.6;        // Tau for SRT collision
    latticeVelocity    0.1;        // Target lattice velocity (Ma ≈ 0.17)
    
    // Derived parameters (calculated automatically)
    reynoldsNumber     1428.57;    // U*L/nu = 0.535*0.04/1.5e-5
    machNumber         0.173;      // U/cs where cs = 1/sqrt(3)
}

CurvedBoundary {
    // Enhanced mesh scaling configuration
    useEnhancedScaling  true;
    
    Geometry {
        cylinder {
            stlFile                "circular_cylinder.stl";
            name                   "cylinder";
            
            // Enhanced scaling parameters
            useEnhancedScaling     true;
            targetCharacteristicLength  26.0;    // Cylinder diameter in lattice units
            scalingDirection       0;            // Scale based on X-direction (diameter)
            preserveAspectRatio    true;
            
            // Position in domain (10D upstream, centered)
            position               < 0.154, 0.25, 0.05 >;  // Physical coordinates
            
            // Force calculation
            calculateForces        true;
            referenceArea          0.04;         // Cylinder frontal area (D*1 for 2D)
            forceOutputFile        "cylinder_forces.dat";
            forceOutputInterval    100;
            
            // Boundary condition
            boundaryCondition      NoSlip;
        }
    }
    
    // Force calculation method
    forceMethod            MOMENTUM_EXCHANGE;    // or STRESS_INTEGRATION, GALILEAN_INVARIANT
    enableForceSmoothing   false;
    
    // Validation settings
    enableValidation       true;
    validationOutputFile   "validation_results.txt";
    
    // Literature benchmarks (from literature_benchmarks.json)
    expectedDragCoefficient     1.33;
    dragCoefficientTolerance    0.05;
    expectedStrouhalNumber      0.164;
    strouhalNumberTolerance     0.005;
}

Turbulence {
    model                  none;    // Start with laminar for Re=100 validation
    // For LES cases:
    // model                Smagorinsky;
    // smagorinskyConstant  0.17;
    // wallDamping          true;
}

BoundaryConditions {
    // Inlet (West boundary)
    inlet {
        type               VelocityBounce;
        velocity           < 0.1, 0.0, 0.0 >;  // Lattice velocity
        turbulenceIntensity 0.01;              // Low turbulence intensity
    }
    
    // Outlet (East boundary)
    outlet {
        type               Outlet;
        method             Neumann;            // Zero gradient
    }
    
    // Top and bottom walls
    walls {
        type               NoSlip;
    }
}

Timeloop {
    timesteps              50000;             // Sufficient for statistical convergence
    
    // Output configuration
    vtkOutput {
        interval           1000;
        filename           "flow_field";
        writeVelocity      true;
        writePressure      true;
        writeVorticity     true;
        writeFlags         true;
        
        // Slice output for 2D visualization
        sliceOutput        true;
        sliceDirection     2;                  // Z-direction
        slicePosition      13;                 // Middle of domain
    }
    
    // Force monitoring
    forceOutput {
        interval           100;
        filename           "forces.dat";
        
        // Real-time monitoring
        consoleOutput      true;
        consoleInterval    1000;
    }
    
    // Convergence monitoring
    convergenceCheck {
        enabled            true;
        checkInterval      1000;
        tolerance          1e-6;
        
        // Monitor drag coefficient convergence
        monitorDragCoefficient  true;
        dragConvergenceTolerance 1e-4;
    }
    
    // Checkpointing
    checkpoint {
        interval           10000;
        filename           "checkpoint";
        enabled            true;
    }
}

Validation {
    // Enable real validation against literature benchmarks
    enabled                true;
    
    // Validation cases to run
    cases {
        cylinderRe100 {
            enabled            true;
            geometryName       "cylinder";
            
            // Literature benchmarks (Williamson 1996)
            expectedResults {
                dragCoefficient    1.33;
                dragTolerance      0.05;
                strouhalNumber     0.164;
                strouhalTolerance  0.005;
                liftRMS            0.25;
                liftRMSTolerance   0.02;
            }
            
            // Validation criteria
            validationMethod   STATISTICAL_CONVERGENCE;
            statisticsWindow   10000;          // Time steps for averaging
            minimumSamples     5000;           // Minimum samples for statistics
        }
    }
    
    // Output configuration
    reportFile             "validation_report.txt";
    generatePlots          true;
    plotFormat             "png";
    
    // Comparison with experimental data
    compareWithLiterature  true;
    literatureBenchmarks   "literature_benchmarks.json";
}

Logging {
    logLevel               INFO;
    logFile                "simulation.log";
    
    // Performance monitoring
    timingOutput           true;
    memoryMonitoring       true;
    
    // Detailed logging for validation
    validationLogging      true;
    forceLogging           true;
}

// MPI configuration
MPI {
    processGrid            < 2, 2, 1 >;       // 4 processes total
    
    // Load balancing
    enableLoadBalancing    true;
    loadBalancingInterval  5000;
}

// Performance optimization
Performance {
    // Memory optimization
    enableMemoryPooling    true;
    
    // Communication optimization
    enableAsynchronousComm true;
    
    // Computational optimization
    enableVectorization    true;
    enableOpenMP           true;
    ompNumThreads          4;
}
