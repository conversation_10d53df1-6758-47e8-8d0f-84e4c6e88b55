//=================================================================================================
/*!
//  \file enhanced_mesh_scaling.h
//  \brief Enhanced mesh scaling header for real waLBerla integration
//  
//  This header defines the enhanced mesh scaling classes that extend
//  waLBerla's curved boundary module with advanced scaling capabilities.
//  No mock components - only real waLBerla interfaces.
*/
//=================================================================================================

#pragma once

#include "core/math/AABB.h"
#include "core/math/Vector3.h"
#include "curvedboundary/mesh/TriangleMesh.h"
#include "curvedboundary/mesh/PhysicalUnitsConverter.h"
#include "curvedboundary/readers/STLReader.h"

#include <memory>
#include <string>

namespace walberla {
namespace curvedboundary {

//=================================================================================================
//  SCALING PARAMETERS AND MODES
//=================================================================================================

enum class ScalingMode {
    PRESERVE_ASPECT,    ///< Preserve aspect ratio (uniform scaling)
    AUTO_FIT,          ///< Fit to target region (may distort)
    TARGET_SIZE        ///< Scale specific dimension to target size
};

struct ScalingParameters {
    real_t scaleFactor = 1.0;           ///< Uniform scaling factor
    Vector3<real_t> translation{0,0,0}; ///< Translation vector
    
    // Non-uniform scaling parameters
    bool nonUniformScaling = false;
    real_t scaleX = 1.0;
    real_t scaleY = 1.0;
    real_t scaleZ = 1.0;
    
    // Target-specific parameters
    uint_t targetDirection = 2;         ///< Direction for target size scaling
    
    ScalingParameters() = default;
};

//=================================================================================================
//  ENHANCED PHYSICAL UNITS CONVERTER
//=================================================================================================

class EnhancedPhysicalUnitsConverter : public PhysicalUnitsConverter {
public:
    /// Constructor with physical parameters
    EnhancedPhysicalUnitsConverter(real_t dx_phys, real_t dt_phys, 
                                  real_t nu_phys = 1.0, real_t rho_phys = 1.0);
    
    /// Set scaling mode
    void setScalingMode(ScalingMode mode);
    
    /// Enable/disable validation
    void enableValidation(bool enable);
    
    /// Calculate enhanced scaling parameters
    ScalingParameters calculateScalingParameters(
        const AABB& meshBounds, 
        real_t targetCharacteristicLength, 
        uint_t direction = 2) const;
    
    /// Get current scaling mode
    ScalingMode getScalingMode() const { return scalingMode_; }
    
    /// Check if validation is enabled
    bool isValidationEnabled() const { return enableValidation_; }

private:
    ScalingMode scalingMode_;
    bool enableValidation_;
    
    /// Validate scaling parameters
    void validateScalingParameters(const ScalingParameters& params,
                                  const AABB& originalBounds,
                                  real_t targetLength) const;
};

//=================================================================================================
//  ENHANCED TRIANGLE MESH
//=================================================================================================

class EnhancedTriangleMesh : public TriangleMesh {
public:
    /// Default constructor
    EnhancedTriangleMesh();
    
    /// Apply enhanced scaling with parameters
    void applyEnhancedScaling(const ScalingParameters& params);
    
    /// Check if mesh has been enhanced scaled
    bool isEnhancedScaled() const { return isEnhancedScaled_; }
    
    /// Get original physical dimensions (before scaling)
    Vector3<real_t> getPhysicalDimensions() const;
    
    /// Get scaling factor applied
    real_t getScalingFactor() const;
    
    /// Get scaling parameters used
    const ScalingParameters& getScalingParameters() const { return scalingParameters_; }
    
    /// Enable/disable preservation of physical dimensions
    void setPreservePhysicalDimensions(bool preserve) { preservePhysicalDimensions_ = preserve; }
    
    /// Validate mesh quality after scaling
    void validateMeshQuality() const;

private:
    bool isEnhancedScaled_;
    bool preservePhysicalDimensions_;
    ScalingParameters scalingParameters_;
    AABB originalBounds_;
    Vector3<real_t> originalPhysicalDimensions_;
    
    /// Apply non-uniform scaling
    void applyNonUniformScaling(real_t scaleX, real_t scaleY, real_t scaleZ);
};

//=================================================================================================
//  ENHANCED STL READER
//=================================================================================================

class EnhancedSTLReader : public STLReader {
public:
    /// Default constructor
    EnhancedSTLReader();
    
    /// Read STL file with enhanced processing
    std::shared_ptr<EnhancedTriangleMesh> readEnhancedSTL(const std::string& filename);
    
    /// Enable/disable preprocessing
    void enablePreprocessing(bool enable) { enablePreprocessing_ = enable; }
    
    /// Enable/disable validation
    void enableValidation(bool enable) { enableValidation_ = enable; }
    
    /// Enable/disable verbose output
    void setVerboseOutput(bool verbose) { verboseOutput_ = verbose; }
    
    /// Check if preprocessing is enabled
    bool isPreprocessingEnabled() const { return enablePreprocessing_; }
    
    /// Check if validation is enabled
    bool isValidationEnabled() const { return enableValidation_; }

private:
    bool enablePreprocessing_;
    bool enableValidation_;
    bool verboseOutput_;
    
    /// Preprocess mesh (remove duplicates, fix normals, etc.)
    void preprocessMesh(EnhancedTriangleMesh& mesh);
    
    /// Remove duplicate vertices
    void removeDuplicateVertices(EnhancedTriangleMesh& mesh);
    
    /// Check mesh consistency
    void checkMeshConsistency(const EnhancedTriangleMesh& mesh);
};

//=================================================================================================
//  UTILITY FUNCTIONS
//=================================================================================================

/// Create enhanced units converter with automatic parameter detection
std::shared_ptr<EnhancedPhysicalUnitsConverter> createEnhancedConverter(
    real_t reynoldsNumber, real_t characteristicLength, real_t characteristicVelocity);

/// Load STL with enhanced scaling applied
std::shared_ptr<EnhancedTriangleMesh> loadSTLWithEnhancedScaling(
    const std::string& filename,
    const EnhancedPhysicalUnitsConverter& converter,
    real_t targetCharacteristicLength,
    uint_t direction = 2);

/// Validate enhanced mesh against literature benchmarks
bool validateEnhancedMesh(const EnhancedTriangleMesh& mesh,
                         const std::string& caseType,
                         real_t reynoldsNumber);

} // namespace curvedboundary
} // namespace walberla
