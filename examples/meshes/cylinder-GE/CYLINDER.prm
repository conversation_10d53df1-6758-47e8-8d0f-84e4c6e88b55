
paraview{
	filename cylinder;
	writeflags 1;
	writevelocity 1;
	//writedensity	1;
	writepressure 1;
	physical 0;
	period_L 100;
	//spacing 50;
	mode BINARY;
 xrange_L 64..64;
	//yrange_L 90..90;
	  //xrange_L 2..2;
}
simdata{
  	domainX         104;//4
        domainY         364;
        domainZ         533;
	dt 		0.0005832;
	dx              0.00312;
	timesteps       70000 ;
	dx_L            1 ;
	dt_L            1 ;
	//Umax          0.01
	
	Uref            0.535;       // (m/s) characteristic Velocity for computation of Re ((physical)
  	
	inflow_L        0.1; // <-- 0.1  // The inflow (lattice) velocity (also used below for
                        // vel_in inflow conditions (0.45 m/s)


	Lref            0.04; // cylinder diameter (physical)
	//nu 0.000001; // 10^-3
	//tau 1.955;
	//tau 0.5001797268;
	rho 1;
	Re  214000 ;
	g_init;
	dump
}



object{
	compressible 0 ;
}

init{
     fill;
     turbulence 1 ;
     Triangle_mesh_scene
     {
 	// =====================================================================
 	//
 	// =====================================================================
 	object{
		stlFileName  Cylinder.stl;
 		density		2000;
 		dynamic ;
 		preprocess{
			//reinverseTheNormals	1 ;
			reComputeTheNormals	1 ;
 			checkNormConsistancy	1 ;
 		}
		lbm_config{
			//position_L <65.0,90.0,65.0>;
			//position_L <2.,90.0,65.0>;
			position_L <52.,182.0,130.0>;
			//objectdomainX	 128;
			//objectdomainY	 10.0;
			objectdomainZ	 26;//25.640 ; // 12.820 ;
			switchBouzidiBCsTo OFF;
			flag <NOSLIP,LIQUID,NEAR_OBST+LIQUID>;
			initial_speed_L<0.01,0.02,0.09>;
		}

		compute{
		        force ;
			drag_coeff{
				D	        0.04  ; // m 
				H               0.56  ; // m 
				rho_infini	1.    ; // kg/m^3
				u_infini_phys	0.535 ; // m/s	
			}
			lift_coeff{
				D	        0.04  ; // m 
				H               0.56  ; // m 
				rho_infini	1.    ; // kg/m^3
				u_infini_phys	0.535 ; // m/s	
			}
		}
	}

	// ======================================================================================================
	floodfill{
		flag <NOSLIP,LIQUID,NEAR_OBST+LIQUID>;	// <inside,outside,border>
		numberOfSeeds	1	;
		seed_L <2,2,2>;
	}
	// ======================================================================================================
	// Export config (optional) :
	// ======================================================================================================
	export{
		results_dir_name	Results;
		use_ascii ;
		lbmstlFileName 	 box_lbm.stl;// it is nice for checking and validation (binary)(output)
		//row_file	 MyFile.raw;// Frank raw file format (binary).

		// computed the pressure at given two point
		/*
			delta_pressure{ 
			p_a <0.205,0.2,0.44>; // point, physic coordinates 
			p_b <0.205,0.2,0.56>;  // point 2
			}
		*/

	}
	}// end Triangle_mesh_scene

	north{// done	
		//noslip{
		freeslip{
		x_L	1..'domainX';
		z_L	1..'domainZ';
		}
	}

		
	south{ // done
		//noslip{
		freeslip{
		x_L	0..'domainX'+1;
		z_L	1..'domainZ';
		}
	}
	
	west{// done
		periodic;
/*
		freeslip{
		y_L	1..'domainY';
		z_L	1..'domainZ';
		}
*/
	}
	east{// done 
		periodic;
/*
		freeslip{
		y_L	1..'domainY';
		z_L	1..'domainZ';
		}
*/
	}
	

	top{
		vel_in{
			x_L	0..'domainX'+1;
			y_L	0..'domainY'+1;
			ux_L	 0.0;
			uy_L	 0.0; //= u m/s *(dt/dx) = 2.0 * (0.001/0.02)
			uz_L	 1.0*'inflow_L';//= u_phy [m/s] *(dt/dx) = 2.0 * (0.001/0.02)
		}
	}
	
	bottom{
		vel_in{
			x_L	0..'domainX'+1;
			y_L	0..'domainY'+1;
			ux_L	 0.0;
			uy_L	 0.0; //= u m/s *(dt/dx) = 2.0 * (0.001/0.02)
			uz_L	 1.0*'inflow_L';//= u_phy [m/s] *(dt/dx) = 2.0 * (0.001/0.02)
		}
	}

}
//End init

patches
{
	xNumPatches 2; //Number of patches in x direction. If not given, then xNumPatches=1
	yNumPatches 2; //Number of patches in y direction. If not given, then yNumPatches=1
	zNumPatches 8; //Number of patches in z direction. If not given, then zNumPatches=1
	autoplace;
}


