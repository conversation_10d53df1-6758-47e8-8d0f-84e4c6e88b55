cmake_minimum_required(VERSION 3.10)
project(EnhancedMeshScaling)

# Set C++ standard
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find waLBerla installation
set(WALBERLA_ROOT "/home/<USER>/walberla")
set(WALBERLA_BUILD_DIR "/home/<USER>/walberla/build/X9DAi_par")

# Add waLBerla include directories
include_directories(
    ${WALBERLA_ROOT}/src
    ${WALBERLA_ROOT}/extern/pe
    ${WALBERLA_ROOT}/extern/GE/src
    ${WALBERLA_BUILD_DIR}
)

# Add waLBerla library directory
link_directories(${WALBERLA_BUILD_DIR}/bin)

# Find required packages
find_package(Boost REQUIRED)
if(Boost_FOUND)
    include_directories(${Boost_INCLUDE_DIRS})
endif()

# Enhanced mesh scaling library
add_library(enhanced_mesh_scaling STATIC
    enhanced_mesh_scaling.cpp
)

target_include_directories(enhanced_mesh_scaling PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${WALBERLA_ROOT}/src
)

# Real validation runner executable
add_executable(real_validation_runner
    real_validation_runner.cpp
)

target_link_libraries(real_validation_runner
    enhanced_mesh_scaling
    walberla
    pe
    ${Boost_LIBRARIES}
)

# Real waLBerla integration test
add_executable(real_walberla_integration
    real_walberla_integration.cpp
)

target_link_libraries(real_walberla_integration
    enhanced_mesh_scaling
    walberla
    pe
    ${Boost_LIBRARIES}
)

# Standalone framework test (already built)
# add_executable(test_real_framework_standalone
#     test_real_framework_standalone.cpp
# )

# Set compiler flags
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O3")

# Print configuration summary
message(STATUS "Enhanced Mesh Scaling Configuration:")
message(STATUS "  waLBerla root: ${WALBERLA_ROOT}")
message(STATUS "  waLBerla build: ${WALBERLA_BUILD_DIR}")
message(STATUS "  Boost found: ${Boost_FOUND}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
